import sys
import uuid
from typing import Dict, Any, <PERSON><PERSON>
from config import Config<PERSON><PERSON><PERSON>
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM,
    OllamaLLM
)
from workflow import GraphBuilder
from dotenv import load_dotenv
load_dotenv()


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """Create an LLM instance based on the provider.

    Args:
        provider: The LLM provider name
        config_manager: The configuration manager

    Returns:
        An instance of the appropriate LLM class

    Raises:
        ValueError: If the provider is not supported
    """
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def setup_application() -> Tuple[ConfigManager, Any]:
    """Set up the application with configuration and LLM.

    Returns:
        A tuple containing the configuration manager and LLM instance
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"Attempting to initialize {llm_provider} LLM...")
        llm = create_llm(llm_provider, config_manager)
    except Exception as e:
        print(f"Error initializing {llm_provider}: {str(e)}")
    return llm


def run_workflow(llm: Any) -> Dict[str, Any]:
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()
    graph_builder.save_graph_image(graph_builder.graph)


    # Create a unique thread ID for this workflow execution
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"Using thread ID: {thread_id}")


    result = graph_builder.invoke_graph({
        "input": """what is ollama?""",
        "source_code": """
        
  CREATE OR REPLACE  PROCEDURE "PAYROLL"."P_ACCOUNTPOSTINGPAYROLL" 

As


 pNum   int;

lv_JournalCategoryName	varchar2(50):=Null;
lv_JournalSourceName	varchar2(50):=Null;
lv_SetOfBooksName	varchar2(50):=Null;
lv_AccountingDate	date:=Null;
lv_CurrencyCode	varchar2(50):=Null;
lv_CurrencyConvType	varchar2(50):=Null;
lv_CurrencyConvDate	varchar2(50):=Null;--modified from date to varchar
lv_CurrencyConvRate	float:=Null;
lv_CompanySegmentValue	varchar2(50):=Null;
lv_LocationSegmentValue	varchar2(50):=Null;
lv_NaturalAccValue	varchar2(50):=Null;
lv_CostCenterSegValue	varchar2(50):=Null;
lv_Future1Value	varchar2(50):=Null;
lv_Future2Value	varchar2(50):=Null;
lv_EnteredDebitAmount	float:=Null;
lv_EnteredCreditAmount	float:=Null;
lv_AccountedDebitAmt	float:=Null;
lv_AccountedCreditAmt	float:=Null;
lv_JournalBatchName	varchar2(50):=Null;
lv_JournalBatcDesc	varchar2(50):=Null;
lv_JournalEntryName	varchar2(50):=Null;
lv_JournalEntryDesc	varchar2(50):=Null;
lv_CreationDate date:=Null;

Begin


pNum := pNum + 1;

Declare
Cursor AccountPosting_Cursor Is
select
       (select JournalCategoryName from journal where journalid=1 and rownum<2)as JournalCategoryName ,--jr.JournalCategoryName as JournalCategoryName ,--eHIS_payroll_Salary
       (select JournalSourceName from journal where journalid=1 and rownum<2) as JournalSourceName,--jr.JournalSourceName as JournalSourceName,--eHIS
       'AHELSOB' as SetOfBooksName,
       sysdate transdate,
       'INR' as CurrencyCode,
       '0000'as CurrencyConvType,
        sysdate as CurrencyCoversionDate,
        0000 as CurrencyConvRate,
       
       ppd.companyid  as CompanySegment,
       ppd.locationid as LocationSegment,
        '7201002',--(Select ORGACCOUNTS.ORGACCCOMPNAYID from ORGACCOUNTS where ORGACCOUNTS.ORGACCOUNTTYPENAME ='StoreDrAccount'),--7201002
        dept.EMP_DEPT_ID, --'000' ,-- dept.EMP_DEPT_ID e.g: 601
        '0000' as FutureValue1,--0000
        '000'as FutureValue2,--000
        sum ( salary.Mon_Ern_Amt ) as EnteredDebitAmount,--<amount>
        0 as EnteredCreditAmount,--<amount>
        sum ( salary.Mon_Ern_Amt ) as AccountedDebitAmount,--<amount>
        0 as AccountedCreditAmount,  --<amount>
        (select journalbatchname from journalbatchcoa where journalid=1 and rownum<2) as JournalBatchName ,--jrcoa.JournalBatchName as JournalBatchName ,--HYD-JHH_Salary
        (select journalbatchdesc from journalbatchcoa where journalid=1 and rownum<2)as JournalBatchDesc,--jrcoa.JournalBatchDesc as JournalBatchDesc ,--Payroll_Salary
        (select journalentryname from journal where journalid=1 and rownum<2)||to_char(ppd.proc_date,'MON')||''''||TO_CHAR(PPD.PROC_DATE,'YY') as JournalEntryName,--jr.journalentryname as JournalEntryName,--Salary_ Apr'07
        (select journalentryname from journal where journalid=1 and rownum<2)||to_char(ppd.proc_date,'MON')||''''||TO_CHAR(PPD.PROC_DATE,'YY') as JournalEntryDesc--Salary_ Apr'07


From pay_monthly_earnings salary
     inner join pay_emp_mst dept on dept.EMP_NO = salary.Mon_Empnumber
     inner join pay_proc_date ppd on DEPT.EMP_AREA_CODE=ppd.locationid

group by dept.EMP_DEPT_ID,ppd.locationid,ppd.companyid,ppd.proc_date,Salary.Mon_Ern_Code
union
select
       (select JournalCategoryName from journal where journalid=1 and rownum<2)as JournalCategoryName ,--jr.JournalCategoryName as JournalCategoryName ,--eHIS_payroll_Salary
       (select JournalSourceName from journal where journalid=1 and rownum<2) as JournalSourceName,--jr.JournalSourceName as JournalSourceName,--eHIS
       'AHELSOB' as SetOfBooksName,--AHELSOB
       sysdate transdate, --<transdate>   --to be modified
       'INR' as CurrencyCode, -- INR
       '0000'as CurrencyConvType,--<>
        sysdate as CurrencyCoversionDate,--<>
        0000 as CurrencyConvRate,--<>
        
       ppd.companyid  as CompanySegment,
       ppd.locationid as LocationSegment,
        '7201002',--(Select ORGACCOUNTS.ORGACCCOMPNAYID from ORGACCOUNTS where ORGACCOUNTS.ORGACCOUNTTYPENAME ='StoreDrAccount'),--7201002
        dept.EMP_DEPT_ID, --'000' ,-- dept.EMP_DEPT_ID e.g: 601
        '0000' as FutureValue1,--0000
        '000'as FutureValue2,--000
        0 EnteredDebitAmount,--<amount>
        sum ( salary.Mon_ded_Amt ) as EnteredCreditAmount,--<amount>
        0 as AccountedDebitAmount,--<amount>
        sum ( salary.Mon_ded_Amt ) as AccountedCreditAmount,  --<amount>
        (select journalbatchname from journalbatchcoa where journalid=1 and rownum<2) as JournalBatchName ,--jrcoa.JournalBatchName as JournalBatchName ,--HYD-JHH_Salary
        (select journalbatchdesc from journalbatchcoa where journalid=1 and rownum<2)as JournalBatchDesc,--jrcoa.JournalBatchDesc as JournalBatchDesc ,--Payroll_Salary
        (select journalentryname from journal where journalid=1 and rownum<2)||to_char(ppd.proc_date,'MON')||''''||TO_CHAR(PPD.PROC_DATE,'YY') as JournalEntryName,--jr.journalentryname as JournalEntryName,--Salary_ Apr'07
        (select journalentryname from journal where journalid=1 and rownum<2)||to_char(ppd.proc_date,'MON')||''''||TO_CHAR(PPD.PROC_DATE,'YY') as JournalEntryDesc--Salary_ Apr'07


From pay_monthly_Deductions salary
     inner join pay_emp_mst dept on dept.EMP_NO = salary.Mon_Empnumber
     inner join pay_proc_date ppd on DEPT.EMP_AREA_CODE=ppd.locationid
group by dept.EMP_DEPT_ID,ppd.locationid,ppd.companyid,ppd.proc_date,Salary.Mon_ded_Code;


      Begin
        update journal  set journal.sequencenumber = pNum where TransactionType =1;

        Open AccountPosting_Cursor;
        Loop
            fetch AccountPosting_Cursor into
             lv_JournalCategoryName,
			       lv_JournalSourceName,
             lv_SetOfBooksName,
			       lv_AccountingDate,
             lv_Currencycode,
			       lv_CurrencyConvType,
             lv_CurrencyConvDate,
			       lv_CurrencyConvRate,
             lv_CompanySegmentValue ,
			       lv_LocationSegmentValue,
             lv_NaturalAccValue,
			       lv_CostCenterSegValue,
             lv_Future1Value,
             lv_Future2Value,
             lv_EnteredDebitAmount,
			       lv_EnteredCreditAmount,
             lv_AccountedDebitAmt,
             lv_AccountedCreditAmt,
			       lv_JournalBatchName ,
             lv_JournalBatcDesc ,
             lv_JournalEntryName,
             lv_JournalEntryDesc
             ;

            Exit when AccountPosting_Cursor%notfound;


             insert into ehisAccounts
               (sequence_num,
                user_je_category_name,
          			user_je_source_name,--JournalSourceName,
                sob_name,--SetOfBooksName,
          			accounting_date,
                currency_code,
          			user_currency_conversion_type,
                currency_conversion_date,
          			currency_conversion_rate,
                segment1,
          			segment2,
                segment3,
          			segment4,
                segment5,
                segment6,
                entered_dr,
          			entered_cr,
                accounted_dr,
                accounted_cr,
          			reference1,
                reference4,
                Date_Created
                )
        			 values
          			(
                 'H'||EHISAccountID_SEQ.Nextval,
                 lv_JournalCategoryName,
    			       lv_JournalSourceName,
                 lv_SetOfBooksName,--need to check again
    			       lv_AccountingDate,--(Select to_char(lv_AccountingDate,'dd-MON-yyyy') from dual),--lv_AccountingDate,
                 lv_Currencycode,
    			       lv_CurrencyConvType,
          		   lv_CurrencyConvDate,--(Select to_char(lv_CurrencyConvDate,'dd-MON-yyyy') from dual),--lv_CurrencyConvDate,
    			       lv_CurrencyConvRate,
                 lv_CompanySegmentValue,
    			       lv_LocationSegmentValue,
                 lv_NaturalAccValue,--To Be Removed
    			       lv_CostCenterSegValue,
                 lv_Future1Value,
                 lv_Future2Value,
                 lv_EnteredDebitAmount,
			           lv_EnteredCreditAmount,
                 lv_AccountedDebitAmt,
                 lv_AccountedCreditAmt,
                 lv_JournalBatchName,
                 lv_JournalEntryName,--need to check again
                 lv_CreationDate
                 );
        End Loop;
        CLOSE AccountPosting_Cursor;
      End;
 commit;

commit;
end P_AccountPostingPayroll;
        """,

        "target_code" : """
        SET search_path TO PAYROLL;

CREATE OR REPLACE PROCEDURE payroll.P_ACCOUNTPOSTINGPAYROLL ()
LANGUAGE plpgsql
SECURITY DEFINER
AS $BODY$
DECLARE
    AccountPosting_Cursor CURSOR FOR
        SELECT
            (
                SELECT
                    JournalCategoryName
                FROM
                    payroll.journal
                WHERE
                    journalid = 1
                LIMIT 1) AS JournalCategoryName,
        (
            SELECT
                JournalSourceName
            FROM
                payroll.journal
            WHERE
                journalid = 1
            LIMIT 1) AS JournalSourceName,
    'AHELSOB' AS SetOfBooksName,
    current_timestamp(0)::timestamp transdate,
    'INR' AS CurrencyCode,
    '0000' AS CurrencyConvType,
    current_timestamp(0)::timestamp AS CurrencyCoversionDate,
    0000 AS CurrencyConvRate,
    ppd.companyid AS CompanySegment,
    ppd.locationid AS LocationSegment,
    '7201002',
    dept.EMP_DEPT_ID,
    '0000' AS FutureValue1,
    '000' AS FutureValue2,
    sum(salary.Mon_Ern_Amt) AS EnteredDebitAmount,
    0 AS EnteredCreditAmount,
    sum(salary.Mon_Ern_Amt) AS AccountedDebitAmount,
    0 AS AccountedCreditAmount,
    (
        SELECT
            journalbatchname
        FROM payroll.journalbatchcoa
        WHERE
            journalid = 1 LIMIT 1) AS JournalBatchName,
(
    SELECT
        journalbatchdesc
    FROM
        payroll.journalbatchcoa
    WHERE
        journalid = 1
    LIMIT 1) AS JournalBatchDesc,
(
    SELECT
        journalentryname
    FROM
        payroll.journal
    WHERE
        journalid = 1
    LIMIT 1) || to_char(ppd.proc_date, 'MON') || '''' || TO_CHAR(PPD.PROC_DATE, 'YY') AS JournalEntryName,

(
    SELECT
        journalentryname
    FROM
        payroll.journal
    WHERE
        journalid = 1
    LIMIT 1) || to_char(ppd.proc_date, 'MON') || '''' || TO_CHAR(PPD.PROC_DATE, 'YY') AS JournalEntryDesc
FROM
    payroll.pay_monthly_earnings salary
    INNER JOIN payroll.pay_emp_mst dept ON dept.EMP_NO = salary.Mon_Empnumber
    INNER JOIN payroll.pay_proc_date ppd ON DEPT.EMP_AREA_CODE = ppd.locationid
   
GROUP BY
    dept.EMP_DEPT_ID,
    ppd.locationid,
    ppd.companyid,
    ppd.proc_date,
    Salary.Mon_Ern_Code
UNION
SELECT
    (
        SELECT
            JournalCategoryName
        FROM
            payroll.journal
        WHERE
            journalid = 1
        LIMIT 1) AS JournalCategoryName,
(
    SELECT
        JournalSourceName
    FROM
        payroll.journal
    WHERE
        journalid = 1
    LIMIT 1) AS JournalSourceName,
'AHELSOB' AS SetOfBooksName,
current_timestamp(0)::timestamp transdate,
'INR' AS CurrencyCode,
'0000' AS CurrencyConvType,
current_timestamp(0)::timestamp AS CurrencyCoversionDate,
0000 AS CurrencyConvRate,
ppd.companyid AS CompanySegment,
ppd.locationid AS LocationSegment,
'7201002',
dept.EMP_DEPT_ID,
'0000' AS FutureValue1,
'000' AS FutureValue2,
0 EnteredDebitAmount,

sum(salary.Mon_ded_Amt) AS EnteredCreditAmount,

0 AS AccountedDebitAmount,

sum(salary.Mon_ded_Amt) AS AccountedCreditAmount,

(
    SELECT
        journalbatchname
    FROM payroll.journalbatchcoa
    WHERE
        journalid = 1 LIMIT 1) AS JournalBatchName,

(
    SELECT
        journalbatchdesc
    FROM
        payroll.journalbatchcoa
    WHERE
        journalid = 1
    LIMIT 1) AS JournalBatchDesc,

(
    SELECT
        journalentryname
    FROM
        payroll.journal
    WHERE
        journalid = 1
    LIMIT 1) || to_char(ppd.proc_date, 'MON') || '''' || TO_CHAR(PPD.PROC_DATE, 'YY') AS JournalEntryName,

(
    SELECT
        journalentryname
    FROM
        payroll.journal
    WHERE
        journalid = 1
    LIMIT 1) || to_char(ppd.proc_date, 'MON') || '''' || TO_CHAR(PPD.PROC_DATE, 'YY') AS JournalEntryDesc

FROM
    payroll.pay_monthly_Deductions salary
    INNER JOIN payroll.pay_emp_mst dept ON dept.EMP_NO = salary.Mon_Empnumber
    INNER JOIN payroll.pay_proc_date ppd ON DEPT.EMP_AREA_CODE = ppd.locationid
    
GROUP BY
    dept.EMP_DEPT_ID,
    ppd.locationid,
    ppd.companyid,
    ppd.proc_date,
    Salary.Mon_ded_Code;
    --loop for cur1
    --
    
    pNum int;
    --params
    lv_JournalCategoryName varchar(50) := NULL;
    lv_JournalSourceName varchar(50) := NULL;
    lv_SetOfBooksName varchar(50) := NULL;
    lv_AccountingDate timestamp without time zone := NULL;
    lv_CurrencyCode varchar(50) := NULL;
    lv_CurrencyConvType varchar(50) := NULL;
    lv_CurrencyConvDate varchar(50) := NULL;
    --modified from date to varchar
    lv_CurrencyConvRate float := NULL;
    lv_CompanySegmentValue varchar(50) := NULL;
    lv_LocationSegmentValue varchar(50) := NULL;
    lv_NaturalAccValue varchar(50) := NULL;
    lv_CostCenterSegValue varchar(50) := NULL;

    
    lv_Future1Value varchar(50) := NULL;
    lv_Future2Value varchar(50) := NULL;
    lv_EnteredDebitAmount float := NULL;
    lv_EnteredCreditAmount float := NULL;
    lv_AccountedDebitAmt float := NULL;
    lv_AccountedCreditAmt float := NULL;
    lv_JournalBatchName varchar(50) := NULL;
    lv_JournalBatcDesc varchar(50) := NULL;
    lv_JournalEntryName varchar(50) := NULL;
    lv_JournalEntryDesc varchar(50) := NULL;
    lv_CreationDate timestamp without time zone := NULL;
    BEGIN
        SET search_path TO PAYROLL;
        
        
        pNum := pNum + 1;
        
        BEGIN
            UPDATE
                payroll.journal
            SET
                journal.sequencenumber = pNum
            WHERE
                TransactionType = 1;
            OPEN AccountPosting_Cursor;
            LOOP
                FETCH AccountPosting_Cursor INTO lv_JournalCategoryName,
                lv_JournalSourceName,
                lv_SetOfBooksName,
                lv_AccountingDate,
                lv_Currencycode,
                lv_CurrencyConvType,
                lv_CurrencyConvDate,
                lv_CurrencyConvRate,
                lv_CompanySegmentValue,
                lv_LocationSegmentValue,
                lv_NaturalAccValue,
                lv_CostCenterSegValue,
                lv_Future1Value,
                lv_Future2Value,
                lv_EnteredDebitAmount,
                lv_EnteredCreditAmount,
                lv_AccountedDebitAmt,
                lv_AccountedCreditAmt,
                lv_JournalBatchName,
                lv_JournalBatcDesc,
                lv_JournalEntryName,
                lv_JournalEntryDesc;
                Exit
                WHEN NOT found;
                INSERT INTO payroll.ehisAccounts (sequence_num,
                    user_je_category_name, user_je_source_name,
                    sob_name,
                    accounting_date, currency_code, user_currency_conversion_type, currency_conversion_date, currency_conversion_rate, segment1, segment2, segment3, segment4, segment5, segment6, entered_dr, entered_cr, accounted_dr, accounted_cr, reference1,
                    reference4,
                    Date_Created
)
                    VALUES ('H' || nextval('payroll.EHISAccountID_SEQ'), lv_JournalCategoryName, lv_JournalSourceName, lv_SetOfBooksName,
                        lv_AccountingDate,
                        lv_Currencycode, lv_CurrencyConvType, lv_CurrencyConvDate,
                        lv_CurrencyConvRate, lv_CompanySegmentValue, lv_LocationSegmentValue, lv_NaturalAccValue,
                        lv_CostCenterSegValue, lv_Future1Value, lv_Future2Value, lv_EnteredDebitAmount, lv_EnteredCreditAmount, lv_AccountedDebitAmt, lv_AccountedCreditAmt, lv_JournalBatchName,
                        lv_JournalEntryName,
                       
                        lv_CreationDate
);
                
            END LOOP;
            CLOSE AccountPosting_Cursor;
        END;
END;
$BODY$;
        """
    }, thread_id=thread_id)
    return result


def main():
    try:
        llm = setup_application()
        result = run_workflow(llm)
        # print("\nWorkflow execution result:", result['mappings'])

        print("\nExecution completed successfully!")

    except ValueError as e:
        print(f"\nConfiguration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
