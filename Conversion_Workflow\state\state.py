from pydantic import BaseModel, Field
from typing import Optional, List, Dict


# class MappingStatement(BaseModel):
#     source_statement: str
#     target_statement: str
#     source_line_number: Optional[str] = None  # Actual line number in source code
#     target_line_number: Optional[str] = None  # Actual line number in target code
#     status: Optional[str] = None


class WorkflowState(BaseModel):
    """Workflow state for code processing pipeline.

    This model tracks the state of code as it moves through the workflow
    """
    source_code: str = Field(
        description="Original Source code to be processed"
    )
    target_code: str = Field(
        description="Original Target code to be processed"
    )
    deployment_error_message: str = Field(
        description="Deployment error message"
    )
    # mappings_statements: List[MappingStatement] = Field(
    #     description="Mappings between source and target code"
    # )
    source_sql_statements: Optional[List[str]] = Field(
        default=None,
        description="Source SQL statements after splitting"
    )
    target_sql_statements: Optional[List[str]] = Field(
        default=None,
        description="Target SQL statements after splitting"
    )
    fixed_code: Optional[str] = Field(
        default=None,
        description="The fixed target code with errors corrected"
    )
    review_results: Optional[Dict] = Field(
        default=None,
        description="Results of the review of the fixed code"
    )



# Define the output schema
class CodeOutput(BaseModel):
    output_code: str = Field(description="The documented code")


class IdentifierPair(BaseModel):
    source_statement: str
    target_statement: str


# class SQLMappingResponse(BaseModel):
#     """Response from SQL mapping operation."""
#     mappings: List[MappingStatement] = Field(
#         description="List of mappings between source and target SQL statements"
#     )


class FixedCodeOutput(BaseModel):
    """Response from error fix operation."""
    fixed_code: str = Field(
        description="The fixed target code with errors corrected"
    )


class ReviewFixedCodeOutput(BaseModel):
    """Response from review fixed code operation."""
    is_properly_fixed: bool = Field(
        description="Whether the error is properly fixed"
    )
    review_comments: str = Field(
        description="Detailed comments about the quality of the fix"
    )
    issues_found: List[str] = Field(
        description="List of any issues found in the fixed code"
    )
    recommendations: List[str] = Field(
        description="Recommendations for improving the fix if needed"
    )



