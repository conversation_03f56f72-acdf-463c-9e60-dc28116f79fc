"""
AI Coding Agent with Azure OpenAI

This script runs the AI Coding Agent using Azure OpenAI with hardcoded credentials.
"""
import os
import sys
import logging

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent_framework.core.agent import Agent
from agent_framework.core.llm_service import LLMService
from agent_framework.tools.tool_registry import ToolRegistry
from agent_framework.memory.memory_manager import MemoryManager
from agent_framework.tools.file_tools import save_file, read_file, list_files, edit_file, remove_files
from agent_framework.tools.code_tools import search_code, analyze_code_structure
from agent_framework.tools.code_retrieval import codebase_retrieval
from agent_framework.tools.terminal_tools import launch_process, read_process, write_process, kill_process, list_processes
from agent_framework.tools.augment_tools import (
    analyze_code_deeply, find_code_relationships, plan_changes,
    diagnostics, analyze_dependencies
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_agent() -> Agent:
    """
    Set up the agent with Azure OpenAI using hardcoded credentials

    Returns:
        Configured Agent instance
    """
    # Hardcoded Azure OpenAI credentials
    api_key = "wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm"
    endpoint = "https://ai-testgeneration707727059630.openai.azure.com/"
    deployment = "gpt4-deployment"
    model = "gpt-4o"
    api_version = "2024-12-01-preview"
    temperature = 0.7
    max_tokens = 4096

    # Create the LLM service with Azure OpenAI
    llm_service = LLMService(
        provider="azure-openai",
        model=model,
        api_key=api_key,
        azure_endpoint=endpoint,
        azure_deployment=deployment,
        api_version=api_version,
        temperature=temperature,
        max_tokens=max_tokens
    )

    # Create the tool registry
    tool_registry = ToolRegistry()

    # Register file tools
    tool_registry.register_tool(
        save_file,
        description="Save content to a file",
        parameter_descriptions={
            "file_path": "Path where to save the file",
            "file_content": "Content to write to the file",
            "add_last_line_newline": "Whether to add a newline at the end"
        }
    )

    tool_registry.register_tool(
        read_file,
        description="Read content from a file",
        parameter_descriptions={
            "file_path": "Path of the file to read"
        }
    )

    tool_registry.register_tool(
        list_files,
        description="List files in a directory",
        parameter_descriptions={
            "directory": "Directory to list files from",
            "pattern": "Optional glob pattern to filter files"
        }
    )

    tool_registry.register_tool(
        edit_file,
        description="Edit a file by replacing text",
        parameter_descriptions={
            "file_path": "Path of the file to edit",
            "old_str": "String to replace",
            "new_str": "Replacement string",
            "line_start": "Optional start line number (1-based)",
            "line_end": "Optional end line number (1-based)"
        }
    )

    tool_registry.register_tool(
        remove_files,
        description="Remove files",
        parameter_descriptions={
            "file_paths": "List of file paths to remove"
        }
    )

    # Register code tools
    tool_registry.register_tool(
        search_code,
        description="Search for code matching a query",
        parameter_descriptions={
            "query": "Search query",
            "directory": "Directory to search in",
            "file_extensions": "List of file extensions to search",
            "max_results": "Maximum number of results to return"
        }
    )

    tool_registry.register_tool(
        analyze_code_structure,
        description="Analyze the structure of a code file",
        parameter_descriptions={
            "file_path": "Path to the code file"
        }
    )

    # Register code retrieval tool
    tool_registry.register_tool(
        codebase_retrieval,
        description="Retrieve information from the codebase using natural language",
        parameter_descriptions={
            "information_request": "Description of the information needed",
            "root_dir": "Root directory of the codebase",
            "max_results": "Maximum number of results to return"
        }
    )

    # Register Augment-like tools
    tool_registry.register_tool(
        analyze_code_deeply,
        description="Perform deep code analysis similar to Augment's code understanding",
        parameter_descriptions={
            "file_path": "Path to the code file to analyze"
        }
    )

    tool_registry.register_tool(
        find_code_relationships,
        description="Find relationships between code files (imports, dependencies, etc.)",
        parameter_descriptions={
            "file_path": "Primary file to analyze",
            "related_paths": "Optional list of related files to check"
        }
    )

    tool_registry.register_tool(
        plan_changes,
        description="Plan changes to a file based on a description",
        parameter_descriptions={
            "file_path": "Path to the file to modify",
            "change_description": "Description of the changes to make"
        }
    )

    tool_registry.register_tool(
        diagnostics,
        description="Get issues (errors, warnings, etc.) from files",
        parameter_descriptions={
            "paths": "Optional list of file paths to check"
        }
    )

    tool_registry.register_tool(
        analyze_dependencies,
        description="Analyze dependencies of a file (imports, requires, etc.)",
        parameter_descriptions={
            "file_path": "Path to the file to analyze",
            "max_depth": "Maximum depth for dependency analysis"
        }
    )

    # Register terminal tools
    tool_registry.register_tool(
        launch_process,
        description="Launch a new process with a shell command",
        parameter_descriptions={
            "command": "The shell command to execute",
            "wait": "Whether to wait for the command to complete",
            "max_wait_seconds": "Number of seconds to wait for the command to complete",
            "cwd": "Working directory for the command"
        }
    )

    tool_registry.register_tool(
        read_process,
        description="Read output from a terminal",
        parameter_descriptions={
            "terminal_id": "Terminal ID to read from",
            "wait": "Whether to wait for the command to complete",
            "max_wait_seconds": "Number of seconds to wait for the command to complete"
        }
    )

    tool_registry.register_tool(
        write_process,
        description="Write input to a terminal",
        parameter_descriptions={
            "terminal_id": "Terminal ID to write to",
            "input_text": "Text to write to the process's stdin"
        }
    )

    tool_registry.register_tool(
        kill_process,
        description="Kill a process by its terminal ID",
        parameter_descriptions={
            "terminal_id": "Terminal ID to kill"
        }
    )

    tool_registry.register_tool(
        list_processes,
        description="List all known terminals and their states",
        parameter_descriptions={}
    )

    # Create the memory manager
    memory_manager = MemoryManager()

    # Create and return the agent
    return Agent(llm_service, tool_registry, memory_manager)

def main():
    """Main function to run the AI Coding Agent with Azure OpenAI"""
    try:
        print("Setting up the Augment-like AI Coding Agent with Azure OpenAI...")
        agent = setup_agent()

        # Add some initial memories to help the agent understand its capabilities
        agent.memory_manager.add_memory(
            "I am an AI coding assistant identical to Augment, designed to help with programming tasks.",
            category="identity"
        )
        agent.memory_manager.add_memory(
            "I should always make a detailed plan before making code changes.",
            category="preference"
        )
        agent.memory_manager.add_memory(
            "I should gather comprehensive information about the code before suggesting changes.",
            category="preference"
        )
        agent.memory_manager.add_memory(
            "I should be thorough in my explanations and provide context for my suggestions.",
            category="preference"
        )
        agent.memory_manager.add_memory(
            "I should follow existing code style and patterns when making changes.",
            category="preference"
        )
        agent.memory_manager.add_memory(
            "I should consider edge cases and error handling in my code suggestions.",
            category="preference"
        )
        agent.memory_manager.add_memory(
            "I should suggest tests for any code changes I recommend.",
            category="preference"
        )
        agent.memory_manager.add_memory(
            "I should break down complex problems into smaller, manageable steps.",
            category="preference"
        )
        agent.memory_manager.add_memory(
            "I should use semantic code search to find relevant code in the codebase.",
            category="code"
        )
        agent.memory_manager.add_memory(
            "I should analyze code structure before suggesting changes.",
            category="code"
        )

        print("\nAugment-like AI Coding Agent is ready!")
        print("Type 'exit' or 'quit' to end the conversation.")
        print("Type 'remember <text>' to add a new memory.")
        print("Type 'remember:code <text>' to add a code-related memory.")
        print("Type 'remember:preference <text>' to add a preference memory.")
        print("Type 'memories' to see your stored memories.")

        while True:
            user_input = input("\nYou: ")

            if user_input.lower() in ["exit", "quit"]:
                print("Goodbye!")
                break

            # Check for memory commands
            if user_input.lower().startswith("remember:"):
                # Extract category and text
                parts = user_input.split(" ", 1)
                if len(parts) < 2 or not parts[1].strip():
                    print("\nAgent: Please provide some text to remember.")
                    continue

                category_part = parts[0].lower()
                memory_text = parts[1].strip()

                if category_part == "remember:code":
                    agent.add_memory(memory_text, category="code")
                    print(f"\nAgent: I'll remember that as code-related knowledge: \"{memory_text}\"")
                elif category_part == "remember:preference":
                    agent.add_memory(memory_text, category="preference")
                    print(f"\nAgent: I'll remember your preference: \"{memory_text}\"")
                else:
                    # Default case
                    agent.add_memory(memory_text)
                    print(f"\nAgent: I'll remember that: \"{memory_text}\"")
                continue

            elif user_input.lower().startswith("remember "):
                memory_text = user_input[9:].strip()
                if memory_text:
                    agent.add_memory(memory_text)
                    print(f"\nAgent: I'll remember that: \"{memory_text}\"")
                    continue

            elif user_input.lower() == "memories":
                # Show a summary of stored memories
                memories = agent.memory_manager.get_all_memories()
                if not memories:
                    print("\nAgent: I don't have any memories stored yet.")
                else:
                    print(f"\nAgent: I have {len(memories)} memories stored. Here are some examples:")
                    # Group by category
                    by_category = {}
                    for memory in memories:
                        category = memory.get("metadata", {}).get("category", "general")
                        if category not in by_category:
                            by_category[category] = []
                        by_category[category].append(memory["content"])

                    # Show examples from each category
                    for category, memory_list in by_category.items():
                        print(f"\n  {category.capitalize()} ({len(memory_list)} memories):")
                        # Show up to 3 examples per category
                        for i, content in enumerate(memory_list[:3]):
                            # Truncate long memories
                            if len(content) > 100:
                                content = content[:97] + "..."
                            print(f"   - {content}")
                        if len(memory_list) > 3:
                            print(f"   - ... and {len(memory_list) - 3} more")
                continue

            print("\nAgent is thinking...")
            response = agent.process_request(user_input)

            print(f"\nAgent: {response}")

    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

def test_memory_system():
    """Test the memory system"""
    print("Testing memory system...")

    # Create agent
    agent = setup_agent()

    # Process a test query
    test_query = "How do I implement a binary search tree in Python?"
    print(f"Processing test query: {test_query}")

    # Process the query
    response = agent.process_request(test_query)
    print(f"Response: {response[:100]}...")

    # Check if the memory was added
    print("\nChecking memories...")
    memories = agent.memory_manager.get_all_memories()

    # Print the last few memories
    for i, memory in enumerate(memories[-5:]):
        print(f"Memory {len(memories) - 5 + i + 1}:")
        print(f"  Content: {memory['content'][:100]}...")
        print(f"  Category: {memory.get('metadata', {}).get('category', 'unknown')}")
        print(f"  Tags: {memory.get('metadata', {}).get('tags', [])}")
        print()

    print("Memory test complete!")

if __name__ == "__main__":
    # Run the main agent
    main()
