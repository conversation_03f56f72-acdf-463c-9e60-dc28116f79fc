"""
Web Tools - Tools for web search and content fetching
"""
import logging
import re
import json
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def web_search(query: str, num_results: int = 5) -> str:
    """
    Search the web for information
    
    Args:
        query: Search query
        num_results: Number of results to return (max 10)
        
    Returns:
        Search results in markdown format
    """
    try:
        # Check if requests is installed
        try:
            import requests
        except ImportError:
            return "Error: The 'requests' package is required for web search. Install it with: pip install requests"
        
        # Limit num_results to a reasonable range
        num_results = max(1, min(10, num_results))
        
        # Mock implementation - in a real system, you would use a search API
        # This is just a placeholder to show the structure
        logger.info(f"Searching for: {query}")
        
        # In a real implementation, you would:
        # 1. Call a search API (Google, Bing, DuckDuckGo, etc.)
        # 2. Parse the results
        # 3. Format them as markdown
        
        # For this example, we'll return mock results
        results = [
            {
                "title": f"Result 1 for '{query}'",
                "url": "https://example.com/result1",
                "snippet": "This is a snippet from the first search result that matches your query."
            },
            {
                "title": f"Result 2 for '{query}'",
                "url": "https://example.com/result2",
                "snippet": "Another snippet from the second search result with relevant information."
            }
        ]
        
        # Format the results as markdown
        markdown = f"# Search Results for: {query}\n\n"
        
        for i, result in enumerate(results[:num_results], 1):
            markdown += f"## {i}. [{result['title']}]({result['url']})\n\n"
            markdown += f"{result['snippet']}\n\n"
            markdown += f"URL: {result['url']}\n\n"
            markdown += "---\n\n"
            
        markdown += f"*Note: This is a mock implementation. In a real system, this would return actual search results.*"
        
        return markdown
    
    except Exception as e:
        error_msg = f"Error performing web search: {str(e)}"
        logger.error(error_msg)
        return error_msg

def web_fetch(url: str) -> str:
    """
    Fetch content from a URL and convert to markdown
    
    Args:
        url: URL to fetch content from
        
    Returns:
        Content in markdown format
    """
    try:
        # Check if required packages are installed
        try:
            import requests
            from bs4 import BeautifulSoup
        except ImportError:
            return "Error: Required packages not installed. Install them with: pip install requests beautifulsoup4"
        
        # Validate URL
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return f"Error: Invalid URL: {url}"
            
        # Fetch the content
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse the HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract the title
        title = soup.title.string if soup.title else "No title found"
        
        # Extract the main content
        # This is a simplified approach - real implementation would be more sophisticated
        main_content = ""
        
        # Try to find the main content container
        main_tags = soup.find_all(['article', 'main', 'div'], class_=re.compile(r'(content|article|post|main)'))
        
        if main_tags:
            # Use the first matching tag
            main_content = main_tags[0].get_text(separator='\n', strip=True)
        else:
            # Fallback to body content
            body = soup.find('body')
            if body:
                main_content = body.get_text(separator='\n', strip=True)
            else:
                main_content = "Could not extract main content"
        
        # Format as markdown
        markdown = f"# {title}\n\n"
        markdown += f"Source: {url}\n\n"
        markdown += "---\n\n"
        
        # Split content into paragraphs and format
        paragraphs = [p for p in main_content.split('\n') if p.strip()]
        
        for p in paragraphs[:30]:  # Limit to 30 paragraphs
            # Clean up the paragraph
            p = re.sub(r'\s+', ' ', p).strip()
            if len(p) > 5:  # Skip very short paragraphs
                markdown += f"{p}\n\n"
        
        if len(paragraphs) > 30:
            markdown += "*Content truncated due to length...*\n\n"
            
        markdown += "---\n\n"
        markdown += f"*Fetched from: {url}*"
        
        return markdown
    
    except Exception as e:
        error_msg = f"Error fetching content from {url}: {str(e)}"
        logger.error(error_msg)
        return error_msg
