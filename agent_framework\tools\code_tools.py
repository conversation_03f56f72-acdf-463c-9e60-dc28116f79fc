"""
Code Tools - Tools for code analysis and retrieval
"""
import os
import re
import logging
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def search_code(query: str, directory: str = ".", file_extensions: Optional[List[str]] = None,
               max_results: int = 10) -> str:
    """
    Search for code matching a query
    
    Args:
        query: Search query
        directory: Directory to search in
        file_extensions: List of file extensions to search (e.g., [".py", ".js"])
        max_results: Maximum number of results to return
        
    Returns:
        Search results as formatted text
    """
    try:
        if not os.path.exists(directory):
            return f"Error: Directory not found: {directory}"
            
        if not os.path.isdir(directory):
            return f"Error: Not a directory: {directory}"
            
        # Default to common code file extensions if none provided
        if file_extensions is None:
            file_extensions = [".py", ".js", ".java", ".c", ".cpp", ".h", ".cs", ".php", ".rb", ".go"]
            
        results = []
        
        # Walk through the directory
        for root, _, files in os.walk(directory):
            for file in files:
                # Check if the file has one of the specified extensions
                if any(file.endswith(ext) for ext in file_extensions):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # Search for the query in the file content
                        if query.lower() in content.lower():
                            # Find matching lines
                            lines = content.splitlines()
                            matching_lines = []
                            
                            for i, line in enumerate(lines):
                                if query.lower() in line.lower():
                                    # Get context (lines before and after)
                                    start = max(0, i - 2)
                                    end = min(len(lines), i + 3)
                                    
                                    context = "\n".join(f"{j+1}: {lines[j]}" for j in range(start, end))
                                    matching_lines.append({
                                        "line_number": i + 1,
                                        "context": context
                                    })
                                    
                                    # Limit the number of matching lines per file
                                    if len(matching_lines) >= 3:
                                        break
                                        
                            if matching_lines:
                                results.append({
                                    "file_path": file_path,
                                    "matching_lines": matching_lines
                                })
                                
                                # Limit the total number of results
                                if len(results) >= max_results:
                                    break
                    
                    except Exception as e:
                        logger.warning(f"Error reading file {file_path}: {str(e)}")
                        
            # Break out of the outer loop if we have enough results
            if len(results) >= max_results:
                break
                
        # Format the results
        if not results:
            return f"No results found for query: {query}"
            
        formatted_results = f"Search results for: {query}\n\n"
        
        for result in results:
            formatted_results += f"📄 {result['file_path']}\n"
            
            for match in result["matching_lines"]:
                formatted_results += f"  Line {match['line_number']}:\n"
                formatted_results += f"```\n{match['context']}\n```\n\n"
                
        return formatted_results
    
    except Exception as e:
        error_msg = f"Error searching code: {str(e)}"
        logger.error(error_msg)
        return error_msg

def analyze_code_structure(file_path: str) -> str:
    """
    Analyze the structure of a code file
    
    Args:
        file_path: Path to the code file
        
    Returns:
        Analysis of the code structure
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
            
        # Get the file extension
        _, ext = os.path.splitext(file_path)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Different analysis based on file type
        if ext.lower() == ".py":
            return _analyze_python_structure(content, file_path)
        elif ext.lower() in [".js", ".ts"]:
            return _analyze_javascript_structure(content, file_path)
        elif ext.lower() in [".java", ".kt"]:
            return _analyze_java_structure(content, file_path)
        else:
            # Generic analysis for other file types
            return _analyze_generic_structure(content, file_path)
    
    except Exception as e:
        error_msg = f"Error analyzing code structure: {str(e)}"
        logger.error(error_msg)
        return error_msg

def _analyze_python_structure(content: str, file_path: str) -> str:
    """Analyze Python code structure"""
    # Find imports
    import_pattern = r"^\s*(import|from)\s+.+$"
    imports = re.findall(import_pattern, content, re.MULTILINE)
    
    # Find classes
    class_pattern = r"^\s*class\s+(\w+)(?:\(([^)]*)\))?"
    classes = re.findall(class_pattern, content, re.MULTILINE)
    
    # Find functions
    function_pattern = r"^\s*def\s+(\w+)\s*\("
    functions = re.findall(function_pattern, content, re.MULTILINE)
    
    # Format the results
    result = f"# Code Structure Analysis: {file_path}\n\n"
    
    if imports:
        result += "## Imports\n"
        for imp in imports[:10]:  # Limit to 10 imports
            result += f"- {imp.strip()}\n"
        if len(imports) > 10:
            result += f"- ... ({len(imports) - 10} more)\n"
        result += "\n"
        
    if classes:
        result += "## Classes\n"
        for class_name, parent_classes in classes:
            if parent_classes:
                result += f"- {class_name} (inherits from: {parent_classes})\n"
            else:
                result += f"- {class_name}\n"
        result += "\n"
        
    if functions:
        result += "## Functions\n"
        for func in functions:
            result += f"- {func}()\n"
        result += "\n"
        
    # Count lines of code
    lines = content.splitlines()
    code_lines = [line for line in lines if line.strip() and not line.strip().startswith("#")]
    
    result += f"## Statistics\n"
    result += f"- Total lines: {len(lines)}\n"
    result += f"- Code lines: {len(code_lines)}\n"
    result += f"- Classes: {len(classes)}\n"
    result += f"- Functions: {len(functions)}\n"
    
    return result

def _analyze_javascript_structure(content: str, file_path: str) -> str:
    """Analyze JavaScript/TypeScript code structure"""
    # Find imports
    import_pattern = r"^\s*(import|require)\s+.+$"
    imports = re.findall(import_pattern, content, re.MULTILINE)
    
    # Find classes
    class_pattern = r"^\s*(class)\s+(\w+)(?:\s+extends\s+(\w+))?"
    classes = re.findall(class_pattern, content, re.MULTILINE)
    
    # Find functions
    function_pattern = r"^\s*(function)\s+(\w+)\s*\("
    functions = re.findall(function_pattern, content, re.MULTILINE)
    
    # Find arrow functions and methods
    arrow_function_pattern = r"(const|let|var)\s+(\w+)\s*=\s*(?:\([^)]*\)|[^=]*)\s*=>"
    arrow_functions = re.findall(arrow_function_pattern, content, re.MULTILINE)
    
    # Format the results
    result = f"# Code Structure Analysis: {file_path}\n\n"
    
    if imports:
        result += "## Imports/Requires\n"
        for imp in imports[:10]:
            result += f"- {imp.strip()}\n"
        if len(imports) > 10:
            result += f"- ... ({len(imports) - 10} more)\n"
        result += "\n"
        
    if classes:
        result += "## Classes\n"
        for _, class_name, parent_class in classes:
            if parent_class:
                result += f"- {class_name} (extends {parent_class})\n"
            else:
                result += f"- {class_name}\n"
        result += "\n"
        
    result += "## Functions\n"
    if functions:
        for _, func_name in functions:
            result += f"- {func_name}()\n"
            
    if arrow_functions:
        for _, func_name in arrow_functions:
            result += f"- {func_name} (arrow function)\n"
    
    if not functions and not arrow_functions:
        result += "- No named functions found\n"
        
    result += "\n"
        
    # Count lines of code
    lines = content.splitlines()
    code_lines = [line for line in lines if line.strip() and not line.strip().startswith("//")]
    
    result += f"## Statistics\n"
    result += f"- Total lines: {len(lines)}\n"
    result += f"- Code lines: {len(code_lines)}\n"
    result += f"- Classes: {len(classes)}\n"
    result += f"- Functions: {len(functions) + len(arrow_functions)}\n"
    
    return result

def _analyze_java_structure(content: str, file_path: str) -> str:
    """Analyze Java/Kotlin code structure"""
    # Find package
    package_pattern = r"^\s*package\s+([^;]+);"
    package_match = re.search(package_pattern, content, re.MULTILINE)
    package = package_match.group(1) if package_match else "Unknown"
    
    # Find imports
    import_pattern = r"^\s*import\s+([^;]+);"
    imports = re.findall(import_pattern, content, re.MULTILINE)
    
    # Find classes and interfaces
    class_pattern = r"^\s*(public|private|protected)?\s*(class|interface|enum)\s+(\w+)(?:\s+extends\s+(\w+))?(?:\s+implements\s+([^{]+))?"
    classes = re.findall(class_pattern, content, re.MULTILINE)
    
    # Find methods
    method_pattern = r"^\s*(public|private|protected)?\s*(?:static\s+)?\w+\s+(\w+)\s*\([^)]*\)"
    methods = re.findall(method_pattern, content, re.MULTILINE)
    
    # Format the results
    result = f"# Code Structure Analysis: {file_path}\n\n"
    
    result += f"## Package\n- {package}\n\n"
    
    if imports:
        result += "## Imports\n"
        for imp in imports[:10]:
            result += f"- {imp.strip()}\n"
        if len(imports) > 10:
            result += f"- ... ({len(imports) - 10} more)\n"
        result += "\n"
        
    if classes:
        result += "## Classes/Interfaces\n"
        for access, type_name, class_name, parent, implements in classes:
            details = []
            if access:
                details.append(access)
            details.append(type_name)
            if parent:
                details.append(f"extends {parent}")
            if implements:
                details.append(f"implements {implements}")
                
            result += f"- {class_name} ({' '.join(details)})\n"
        result += "\n"
        
    if methods:
        result += "## Methods\n"
        for _, method_name in methods:
            result += f"- {method_name}()\n"
        result += "\n"
        
    # Count lines of code
    lines = content.splitlines()
    code_lines = [line for line in lines if line.strip() and not line.strip().startswith("//")]
    
    result += f"## Statistics\n"
    result += f"- Total lines: {len(lines)}\n"
    result += f"- Code lines: {len(code_lines)}\n"
    result += f"- Classes/Interfaces: {len(classes)}\n"
    result += f"- Methods: {len(methods)}\n"
    
    return result

def _analyze_generic_structure(content: str, file_path: str) -> str:
    """Generic code structure analysis for unsupported file types"""
    lines = content.splitlines()
    
    # Count lines of code
    non_empty_lines = [line for line in lines if line.strip()]
    
    # Try to identify functions/methods using common patterns
    function_patterns = [
        r"^\s*(?:function|def|func|sub|procedure|method)\s+(\w+)",  # Various function keywords
        r"^\s*(\w+)\s*\([^)]*\)\s*{",  # C-style functions
        r"^\s*(\w+)\s*:\s*function"  # JavaScript object methods
    ]
    
    functions = []
    for pattern in function_patterns:
        functions.extend(re.findall(pattern, content, re.MULTILINE))
    
    # Format the results
    result = f"# Code Structure Analysis: {file_path}\n\n"
    
    if functions:
        result += "## Possible Functions/Methods\n"
        for func in functions:
            result += f"- {func}\n"
        result += "\n"
    
    # Look for possible class/struct definitions
    class_patterns = [
        r"^\s*(?:class|struct|interface|trait|enum)\s+(\w+)",
        r"^\s*type\s+(\w+)\s+struct"
    ]
    
    classes = []
    for pattern in class_patterns:
        classes.extend(re.findall(pattern, content, re.MULTILINE))
    
    if classes:
        result += "## Possible Classes/Types\n"
        for cls in classes:
            result += f"- {cls}\n"
        result += "\n"
    
    result += f"## Statistics\n"
    result += f"- Total lines: {len(lines)}\n"
    result += f"- Non-empty lines: {len(non_empty_lines)}\n"
    result += f"- Possible functions: {len(functions)}\n"
    result += f"- Possible classes: {len(classes)}\n"
    
    return result
