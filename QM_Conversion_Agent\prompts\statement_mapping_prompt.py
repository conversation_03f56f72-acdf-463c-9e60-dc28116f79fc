"""
Prompts for SQL statement mapping in database conversion.
"""
from typing import List, Dict

def create_statement_mapping_prompt(source_statements: List[str], target_statements: List[str], batch_size: int = 10) -> str:
    """
    Creates a prompt for mapping source statements to target statements.

    This function creates a prompt that instructs the LLM to analyze and map statements
    between source and target databases. It processes statements in batches to avoid
    overwhelming the LLM.

    Args:
        source_statements: List of source SQL statements to map
        target_statements: List of target SQL statements to map
        batch_size: Number of statements to process in each batch

    Returns:
        A formatted prompt string for the LLM
    """
    # Get the batch of statements to process
    source_batch = source_statements[:batch_size]
    target_batch = target_statements[:batch_size]

    # Format the statements for the prompt
    source_formatted = "\n".join([f"{i+1}. {stmt}" for i, stmt in enumerate(source_batch)])
    target_formatted = "\n".join([f"{i+1}. {stmt}" for i, stmt in enumerate(target_batch)])

    return f"""
    You are a database database Oracle to Postgres mapping expert for the give data. Analyze these source statements and target statements
    and create a detailed mapping between them.

    IMPORTANT: The statements provided have already been correctly split. DO NOT split them further.
    Treat each numbered statement as a complete, atomic unit regardless of any semicolons, AS/IS keywords,
    or other SQL syntax it may contain. The statements may contain comments, quoted text with semicolons,
    or other complex SQL constructs that should be preserved exactly as they are.

    Source statements:
    {source_formatted}

    Target statements:
    {target_formatted}

    For each statement, determine if it exists in both source and target (Matched),
    only in source (Source Only), or only in target (Target Only).

    IMPORTANT: Use exactly these status values: "Matched", "Source Only", or "Target Only".

    CRITICAL: Compare EVERY source statement with EVERY target statement, not just by position or line number.
    You must:
    1. For each source statement, compare it with ALL target statements to find a match
    2. For each target statement, compare it with ALL source statements to find a match

    DO NOT assume that statements at the same position or with similar line numbers are related.
    The matching must be based ONLY on the content and meaning of the statements.

    Use semantic understanding to match statements, not just exact text matching.
    Consider that the same operation might be expressed differently in different database dialects.

    CRITICAL FOR MATCHING: You MUST ignore ALL differences in formatting, whitespace, and line breaks when determining if statements match.

    Two SQL statements should be considered EQUIVALENT when:
    - They perform the same database operation
    - They use the same SQL keywords, variables, and identifiers
    - They would produce the same result when executed

    Statements should be considered equivalent REGARDLESS of:
    - Different indentation or leading/trailing whitespace
    - Different line breaks (keywords or variables on different lines)
    - Different spacing between elements
    - Comma placement (before or after line breaks)

    For SQL statements, focus ONLY on the semantic meaning and operation, completely ignoring formatting differences.
    When comparing statements, mentally normalize them by removing extra whitespace and joining lines.

    Return the mappings in a structured format. Each mapping should include the EXACT, COMPLETE statement
    as provided in the input, without any modifications, splitting, or reformatting.

    DO NOT:
    1. Split statements further
    2. Format or reformat the SQL
    3. Change indentation or whitespace
    4. Modify comments or quoted text
    5. Add or remove semicolons
    6. Make any other changes to the statements

    The statements must be returned EXACTLY as they were provided in the input.
    """


def create_verification_mapping_prompt(source_mappings: List, target_mappings: List, context_mappings: List[Dict[str, str]]) -> str:
    """
    Creates a prompt for verification mapping.

    This function creates a prompt that instructs the LLM to analyze source-only and target-only
    statements and identify potential matches between them, using existing matches as context.

    Args:
        source_mappings: List of source-only statement mappings
        target_mappings: List of target-only statement mappings
        context_mappings: List of existing matched mappings to provide context

    Returns:
        A formatted prompt string for the LLM
    """
    # Extract the actual statements from the mappings
    source_texts = [mapping.source_statement for mapping in source_mappings]
    target_texts = [mapping.target_statement for mapping in target_mappings]

    # Format the statements for the prompt
    source_formatted = "\n".join([f"{i+1}. {stmt}" for i, stmt in enumerate(source_texts)])
    target_formatted = "\n".join([f"{i+1}. {stmt}" for i, stmt in enumerate(target_texts)])

    # Format the context mappings
    context_formatted = ""
    if context_mappings:
        context_formatted = "Here are some examples of statements that have already been matched:\n\n"
        for i, mapping in enumerate(context_mappings):
            context_formatted += f"Example {i+1}:\n"
            context_formatted += f"Source: {mapping['source_statement']}\n"
            context_formatted += f"Target: {mapping['target_statement']}\n\n"

    return f"""
    You are a database database Oracle to Postgres mapping expert for the give data. Analyze these source-only and target-only SQL statements
    and identify any potential matches between them.

    {context_formatted}

    Source statements (currently unmatched):
    {source_formatted}

    Target statements (currently unmatched):
    {target_formatted}

    For each source statement, determine if it has a matching target statement.

    IMPORTANT: Use semantic understanding to match statements, not just exact text matching.
    Consider that the same operation might be expressed differently in different database dialects.

    CRITICAL FOR MATCHING: You MUST ignore ALL differences in formatting, whitespace, and line breaks when determining if statements match.

    Two SQL statements should be considered EQUIVALENT when:
    - They perform the same database operation
    - They use the same SQL keywords, variables, and identifiers
    - They would produce the same result when executed

    Statements should be considered equivalent REGARDLESS of:
    - Different indentation or leading/trailing whitespace
    - Different line breaks (keywords or variables on different lines)
    - Different spacing between elements
    - Comma placement (before or after line breaks)

    For SQL statements, focus ONLY on the semantic meaning and operation, completely ignoring formatting differences.
    When comparing statements, mentally normalize them by removing extra whitespace and joining lines.

    Return ONLY the pairs of matching statements in this format:
    [
      {{
        "source_index": 1,  # 1-based index in the source list above
        "target_index": 3,  # 1-based index in the target list above
        "confidence": 0.85  # A number between 0 and 1 indicating match confidence
      }},
      # more matches...
    ]

    Only include matches where you have at least 70% confidence (0.7 or higher).
    If there are no matches, return an empty array: []
    """
