import streamlit as st
import sys
import os
from db_tools import process_migration

# Set page configuration
st.set_page_config(
    page_title="Oracle to PostgreSQL Migration Expert",
    page_icon="🔄",
    layout="wide",
)

# Add custom CSS for an industry-standard interface
st.markdown("""
<style>
    /* Base styles */
    .main {
        padding: 1.5rem;
        background-color: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    /* Text area styling - more standard and professional */
    .stTextArea textarea {
        font-family: 'Courier New', Courier, monospace;
        font-size: 14px;
        line-height: 1.5;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: #ffffff;
        padding: 8px;
    }

    .stTextArea textarea:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Placeholder text styling */
    .stTextArea textarea::placeholder {
        color: #6c757d;
        opacity: 0.7;
    }

    /* Header styling - clean and professional */
    .header-container {
        padding: 1.5rem 0;
        margin-bottom: 2rem;
        border-bottom: 1px solid #dee2e6;
    }

    .header-text h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: #212529;
    }

    .header-text p {
        margin: 0.5rem 0 0 0;
        font-size: 1rem;
        color: #6c757d;
    }

    /* Form and input styling */
    .form-container {
        background-color: white;
        border-radius: 4px;
        padding: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        margin-bottom: 1.5rem;
        border: 1px solid #dee2e6;
    }

    .input-label {
        font-weight: 500;
        color: #212529;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    /* Standard button styling */
    .stButton>button {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .stButton>button:hover {
        color: #fff;
        background-color: #0069d9;
        border-color: #0062cc;
    }

    .stButton>button:focus {
        box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
    }

    /* Download button */
    .stDownloadButton>button {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .stDownloadButton>button:hover {
        color: #fff;
        background-color: #218838;
        border-color: #1e7e34;
    }

    .stDownloadButton>button:focus {
        box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
    }

    /* Headings */
    h3 {
        font-weight: 500;
        color: #212529;
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    /* Result cards */
    .result-card {
        background-color: white;
        border-radius: 4px;
        padding: 1.25rem;
        margin: 1rem 0;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid #dee2e6;
    }

    /* Code display */
    pre {
        background-color: #f8f9fa;
        color: #212529;
        border-radius: 4px;
        padding: 1rem;
        border: 1px solid #dee2e6;
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        font-size: 0.875rem;
        overflow-x: auto;
    }

    /* Alerts */
    .stAlert {
        border-radius: 4px;
        margin: 1rem 0;
    }

    /* Footer */
    .footer {
        margin-top: 3rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
        text-align: center;
        color: #6c757d;
        font-size: 0.875rem;
    }
</style>
""", unsafe_allow_html=True)

# Standard Bootstrap-like header
st.markdown(
    """
    <div class="header-container">
        <div class="header-text">
            <h1>Oracle to PostgreSQL Migration Expert</h1>
            <p>Fix deployment errors in PostgreSQL code migrated from Oracle</p>
        </div>
    </div>
    """,
    unsafe_allow_html=True
)

# Main content with friendly heading
st.header("Enter Your Code")

# Create tabs for a more organized interface
tab1, tab2, tab3 = st.tabs(["Oracle Source", "PostgreSQL Target", "Error Message"])

with tab1:
    source_code = st.text_area(
        label="",
        height=250,
        placeholder="-- Enter the original Oracle SQL/PL-SQL code here\nCREATE OR REPLACE PROCEDURE example_proc(p_id IN NUMBER) IS\nBEGIN\n  INSERT INTO target_table\n  SELECT * FROM source_table\n  WHERE id = p_id;\nEND;",
        help="Paste the original Oracle SQL or PL/SQL code here"
    )

with tab2:
    target_code = st.text_area(
        label="",
        height=250,
        placeholder="-- Enter the PostgreSQL code with errors here\nCREATE OR REPLACE PROCEDURE example_proc(p_id IN NUMERIC)\nAS $$\nBEGIN\n  INSERT INTO target_table\n  SELECT * FROM source_table\n  WHERE id = p_id;\nEND;\n$$ LANGUAGE plpgsql;",
        help="Paste the PostgreSQL code that has deployment errors"
    )

with tab3:
    error_message = st.text_area(
        label="",
        height=150,
        placeholder="ERROR: syntax error at or near \"IN\"",
        help="Paste the error message from PostgreSQL deployment"
    )

# Always enable the button - we'll handle validation after submission
col1, col2, col3 = st.columns([3, 1, 3])
with col2:
    submitted = st.button("Submit", type="primary")

# Track if we should show validation message
show_validation = False

# Space before processing section
st.markdown("<br>", unsafe_allow_html=True)

# Process the migration when button is clicked
if submitted:
    # Check if all fields are filled
    all_fields_filled = bool(source_code.strip() and target_code.strip() and error_message.strip())

    if not all_fields_filled:
        # Show validation message if any field is empty
        missing_fields = []
        if not source_code.strip():
            missing_fields.append("Oracle Source")
        if not target_code.strip():
            missing_fields.append("PostgreSQL Target")
        if not error_message.strip():
            missing_fields.append("Error Message")

        st.error(f"Please fill in all fields: {', '.join(missing_fields)}")
    else:
        # All fields are filled, proceed with processing
        try:
            with st.spinner("Analyzing and fixing migration issues..."):
                # Process the migration
                result = process_migration(source_code, target_code, error_message)

                # Check if there was an error
                if "error" in result:
                    st.error(f"Error: {result['error']}")
                    st.info("Please check your inputs and try again.")
                else:
                    # Display friendly success message
                    st.success("✅ Success! Your code has been fixed.")

                    # Results heading with friendly message
                    st.header("Results")

                    # Create tabs for results
                    result_tab1, result_tab2, result_tab3, result_tab4 = st.tabs([
                        "Fixed Code",
                        "Summary",
                        "Differences",
                        "Recommendations"
                    ])

                    # Tab 1: Fixed Code
                    with result_tab1:
                        # Display the fixed code
                        fixed_code = result.get("fixed_code", "")
                        st.code(fixed_code, language="sql")

                        # Make sure fixed_code is a string before downloading
                        if fixed_code and isinstance(fixed_code, str):
                            # Download button - not full width
                            col1, col2, col3 = st.columns([1, 2, 1])
                            with col2:
                                st.download_button(
                                    label="Download SQL",
                                    data=fixed_code,
                                    file_name="fixed_postgresql_code.sql",
                                    mime="text/plain"
                                )
                        else:
                            st.warning("No code available to download")

                    # Tab 2: Summary
                    with result_tab2:
                        st.subheader("Summary")
                        st.markdown(result.get("summary", ""))

                    # Tab 3: Oracle vs PostgreSQL Differences
                    with result_tab3:
                        st.subheader("Oracle vs PostgreSQL Differences")
                        if result.get("explanation"):
                            st.markdown(result.get("explanation", ""))
                        else:
                            st.info("No difference explanation available")

                    # Tab 4: Recommendations
                    with result_tab4:
                        st.subheader("Migration Recommendations")
                        if result.get("recommendations"):
                            for i, rec in enumerate(result.get("recommendations", []), 1):
                                st.markdown(f"**{i}.** {rec}")

                            # Additional information if available
                            if result.get("additional_info"):
                                st.subheader("Additional Information")
                                st.markdown(result.get("additional_info", ""))
                        else:
                            st.info("No recommendations available")

        except Exception as e:
            st.error(f"An unexpected error occurred: {str(e)}")
            st.info("Please try again with different inputs or contact support if the issue persists.")

# Standard footer
st.markdown(
    """
    <div class="footer">
        <p>Oracle to PostgreSQL Migration Expert</p>
        <p>Powered by AI | Simplifying database migrations</p>
        <p>© 2023 QMigrator AI</p>
    </div>
    """,
    unsafe_allow_html=True
)
