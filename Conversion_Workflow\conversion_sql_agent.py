# from langchain_openai import AzureChatOpenAI
# from langchain_community.agent_toolkits.sql.base import create_sql_agent
# from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
# from langchain_community.utilities import SQLDatabase

# llm = AzureChatOpenAI(
#     azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
#     azure_deployment="gpt4-deployment",
#     api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
#     api_version="2024-12-01-preview",
#     temperature="0.3",
# )
# print(llm)

# db = SQLDatabase.from_uri('postgresql+psycopg2://qmig:Qu%40drant%40754@**********/qmigv2testdb?options=-c search_path=qmigrator_ai')
# print(db)


# toolkit = SQLDatabaseToolkit(db=db,llm=llm)

# agent_executor = create_sql_agent(
#     llm=llm,
#     toolkit=toolkit,
#     verbose=True,
#     handle_parsing_errors=True
# )

# # response = agent_executor.invoke({
# #     "input": "List all columns and their data types in the admin_key_areas table."
# # })
# # print(response)

# # response = agent_executor.invoke({
# #     "input": "can you provide database size query then execute and gives the result?"
# # })
# # print(response)

# # response = agent_executor.invoke({
# #     "input": "is there any indexes created in the database?"
# # })
# # print(response)


# response = agent_executor.invoke({
#     "input": """
#     provide the database performance issues if any?
#     """
# })
# print(response)









from langchain_openai import AzureChatOpenAI
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor
from langchain.agents.agent import AgentOutputParser
from langchain.agents.react.agent import create_react_agent
from langchain_core.prompts import PromptTemplate
from langchain_core.exceptions import OutputParserException
from langchain_core.agents import AgentAction, AgentFinish
import re

# Create a custom output parser that's more forgiving
class CustomReActOutputParser(AgentOutputParser):
    def parse(self, text: str) -> AgentAction | AgentFinish:
        # Check if the output indicates a final answer
        if "Final Answer:" in text:
            # Extract the final answer
            match = re.search(r"Final Answer:(.*?)(?:\n|$)", text, re.DOTALL)
            if match:
                final_answer = match.group(1).strip()
                return AgentFinish(
                    return_values={"output": final_answer},
                    log=text,
                )
            else:
                # If we can't extract the final answer, use the whole text
                return AgentFinish(
                    return_values={"output": text.strip()},
                    log=text,
                )

        # Try to extract action and action input
        action_match = re.search(r"Action:(.*?)(?:\n|$)", text)
        action_input_match = re.search(r"Action Input:(.*?)(?:\n|$)", text)

        if action_match and action_input_match:
            action = action_match.group(1).strip()
            action_input = action_input_match.group(1).strip()
            return AgentAction(tool=action, tool_input=action_input, log=text)

        # If we can't find both action and action input, try to be more lenient
        if action_match:
            action = action_match.group(1).strip()
            # If we have an action but no input, use an empty string as input
            return AgentAction(tool=action, tool_input="", log=text)

        # If we can't parse the output at all, raise an exception
        raise OutputParserException(f"Could not parse LLM Output: {text}")

llm = AzureChatOpenAI(
    azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
    azure_deployment="gpt4-deployment",
    api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
    api_version="2024-12-01-preview",
    temperature="0.3",
)
print(llm)

db = SQLDatabase.from_uri('postgresql+psycopg2://qmig:Qu%40drant%40754@**********/qmigv2testdb?options=-c search_path=qmigrator_ai')
print(db)

toolkit = SQLDatabaseToolkit(db=db, llm=llm)

# Create a prompt template for the ReAct agent
template = '''You are a SQL database expert who analyzes database performance issues. You have access to the following tools:

{tools}

You MUST follow this EXACT format for EVERY response:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

After "Thought:", you MUST ALWAYS follow with "Action:" and then "Action Input:".
Never skip directly from "Thought:" to "Final Answer:".
Always use at least one tool before giving a Final Answer.

Begin!

Question: {input}
Thought:{agent_scratchpad}'''

prompt = PromptTemplate.from_template(template)

# Create a custom output parser
output_parser = CustomReActOutputParser()

# Create a ReAct agent
react_agent = create_react_agent(
    llm=llm,
    tools=toolkit.get_tools(),
    prompt=prompt,
    output_parser=output_parser,
    stop_sequence=["Observation:"]
)

# Create the agent executor
agent_executor = AgentExecutor.from_agent_and_tools(
    agent=react_agent,
    tools=toolkit.get_tools(),
    verbose=True,
    handle_parsing_errors=True,
    max_iterations=20,  # Further increased iterations to allow for more complex analysis
    early_stopping_method="force"  # Force stop after max_iterations
)

# response = agent_executor.invoke({
#     "input": "List all columns and their data types in the admin_key_areas table."
# })
# print(response)

# response = agent_executor.invoke({
#     "input": "can you provide database size query then execute and gives the result?"
# })
# print(response)

# response = agent_executor.invoke({
#     "input": "is there any indexes created in the database?"
# })
# print(response)

# Execute the agent with a simple query to test functionality
response = agent_executor.invoke({
    "input": """what is the cpu and memory usage of the database?"""
})
print(response)



# from langchain_openai import AzureChatOpenAI
# from langchain.agents import AgentExecutor
# from langchain.agents import create_openai_tools_agent
# from langchain import hub
# import psycopg2
# from langchain_core.tools import tool



# prompt=hub.pull("hwchase17/openai-functions-agent")
# prompt.messages


# llm = AzureChatOpenAI(
#     azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
#     azure_deployment="gpt4-deployment",
#     api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
#     api_version="2024-12-01-preview",
#     temperature="0.3",
# )

# @tool
# def get_database_tables() -> list:
#     """Get the list of tables in the database.

#     Returns:
#         A list of dictionaries containing table names
#     """
#     connection = psycopg2.connect(user="postgres", password="postgres",
#                                       host="localhost", database='postgres',
#                                       port=5432)
#     cursor = connection.cursor()

#     cursor.execute("""SELECT table_name FROM information_schema.tables WHERE table_type = 'BASE TABLE' AND table_schema NOT IN ('pg_catalog', 'information_schema');""")
#     data = cursor.fetchall()

#     # Format the results as a list of dictionaries
#     tables_list = [{"tablename": tablename[0]} for tablename in data]
#     print(tables_list)
#     return tables_list





# tools = [get_database_tables]

# agent = create_openai_tools_agent(llm, tools, prompt)
# agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# response = agent_executor.invoke({"input":"provide list of tables in the database"})
# print(response)
