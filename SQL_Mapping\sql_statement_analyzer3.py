from langchain_openai import AzureChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
import csv
from typing import List, Dict
import re, json


class SQLStatementAnalyzer:
    def __init__(self, api_key: str, azure_endpoint: str, deployment_name: str):
        self.client = AzureChatOpenAI(
            azure_endpoint=azure_endpoint,
            azure_deployment=deployment_name,
            api_key=api_key,
            api_version="2024-12-01-preview",
            temperature=0.1,
        )

    def analyze_sql_files(self, source_content: str, target_content: str) -> List[Dict]:
        """Analyze and map SQL statements between source and target using Azure OpenAI."""

        prompt = f"""
        You are a SQL migration expert. Analyze these two SQL procedures (Oracle source and PostgreSQL target)
        and create a detailed mapping following these rules:

           - RULE 1: CRITICAL: Split data at semicolons (;) - each semicolon marks the end of a separate statement
           - RULE 2: CRITICAL: IGNORE semicolons that appear inside SQL comments (-- single line or /* multi-line */)
           - RULE 3: CRITICAL: Before the first semicolon, look for exact word matches of "as" or "is" using regex word boundaries (\\bas\\b|\\bis\\b) in any case (uppercase, lowercase, or mixed case)
           - RULE 4: CRITICAL: When "as" or "is" is found before the first semicolon, split at that point (include everything up to and including the "as" or "is" word)
           - RULE 5: CRITICAL: After splitting at "as" or "is", continue splitting at each semicolon. For example, "ON_RegistrationID OUT NUMBER) is LV_Patient NUMBER; sEQUENCENAME VARCHAR(100);" should be split into "ON_RegistrationID OUT NUMBER) is" and "LV_Patient NUMBER;" and "sEQUENCENAME VARCHAR(100);"
           - RULE 6: EXTREMELY CRITICAL: Each semicolon marks the end of a separate statement. If there's no semicolon after a statement, it MUST be combined with the next statement until a semicolon is found. For example, "if LV_Patient is null then SELECT Trim(extract(v_RegistrationLocation, '/RegistrationRequest/Patient/@LocationID').GETSTRINGVAL()) into V_LOCATION FROM dual;" should be treated as a single statement because there's no semicolon after "then". Similarly, "BEGIN v_Patient := XMLTYPE(Iclob_PatientDetails);" should be a single statement
           - RULE 7: CRITICAL: In PostgreSQL, when you find "AS" followed by any dollar-quoted string (like "$$", "$body$", "$procedure$", etc.), you MUST split them. Include "AS" in the first statement but put the dollar-quoted string with the next statement
           - RULE 8: CRITICAL: The "as"/"is" splitting rule only applies before the first semicolon in the entire object/file
           - RULE 9: CRITICAL: Keep the semicolon with its statement
           - RULE 10: EXTREMELY CRITICAL: Do NOT split ending patterns like "END;$$;", "END;$body$;", "END;$procedure$;" in PostgreSQL procedures - treat these entire patterns with dollar-quoted strings as single units. For example, "END;$BODY$;" should be kept as a single statement and not split into "END;" and "$BODY$;"
           - RULE 10a: HIGHEST PRIORITY: When mapping source "END;" to target "END;$BODY$;", always include the full "END;$BODY$;" in the target statement
           - RULE 11: CRITICAL: First, remove all empty lines from the source and target SQL files
           - RULE 12: CRITICAL: After splitting the statements based on the rules, number them sequentially (1, 2, 3, 4, 5...) in the order they appear in each file
           - RULE 13: CRITICAL: For statements that exist only in the target and not in the source, mark them as "Target Only", set source_statement to empty string, and source_line_number to "-"
           - RULE 14: CRITICAL: For statements that exist only in the source and not in the target, mark them as "Source Only", set target_statement to empty string, and target_line_number to "-"
           - RULE 15: EXTREMELY CRITICAL: Do NOT add keywords that don't exist in the original code or statements. Copy the code EXACTLY as it appears in the original
           - RULE 15a: HIGHEST PRIORITY: When the target has a block format like "DECLARE [multiple variable declarations]", preserve the exact format. For PostgreSQL, the DECLARE block should be treated as a single unit with the format "DECLARE" followed by variable declarations. Do NOT add "DECLARE" to each individual variable when mapping
           - RULE 16: EXTREMELY CRITICAL: Do NOT remove or modify ANY original code in source/target statements. Copy the code EXACTLY as it appears in the original, including all keywords, operators, and punctuation. For example, "if LV_Patient is null then" must be preserved exactly as is, not changed to "IF LV_Patient IS NULL THEN" or any other variation.
           - RULE 17: EXTREMELY CRITICAL: BEGIN without a semicolon MUST be combined with the next statement. For example, if you see "BEGIN" without a semicolon, it MUST be combined with the next statement like "BEGIN v_Patient := XMLTYPE(Iclob_PatientDetails);". Similarly, "if", "else", "end if" without semicolons MUST be combined with their next statements.
           - RULE 18: EXTREMELY CRITICAL: All statements ending with a semicolon MUST be split into separate statements, including complex statements. For example, "INSERT INTO Patient (RegistrationID, ...) VALUES (LV_Patient, ...);" should be a separate statement even if it spans multiple lines. Similarly, "v_Patient := XMLTYPE(Iclob_PatientDetails);" and "v_createddate := sysdate;" should be separate statements.
        Source SQL:
        {source_content}

        Target SQL:
        {target_content}

        Return the response strictly in this exact format without markdowns:
        {{"response": [
            {{
                "source_statement": "[Source SQL statement]",
                "target_statement": "[Target SQL statement]",
                "source_line_number": "[Sequential number]",
                "target_line_number": "[Sequential number]",
                "status": "[Mapped/Source Only/Target Only]"
            }},
            {{
                "source_statement": "[Source SQL statement]",
                "target_statement": "",
                "source_line_number": "[Sequential number]",
                "target_line_number": "-",
                "status": "Source Only"
            }},
            {{
                "source_statement": "",
                "target_statement": "[Target SQL statement]",
                "source_line_number": "-",
                "target_line_number": "[Sequential number]",
                "status": "Target Only"
            }}
        ]}}
        """

        # Get analysis from Azure OpenAI using LangChain interface
        messages = [
            SystemMessage(content="You are a SQL migration expert specializing in mapping Oracle SQL to PostgreSQL. Follow ALL rules in the prompt EXACTLY, especially rules 6, 16, 17, and 18 which are EXTREMELY CRITICAL. Return the response in the exact format {\"response\": [array of mappings]}"),
            HumanMessage(content=prompt)
        ]

        response_data = self.client.invoke(messages)
        response_text = response_data.content
        print("Raw response:", response_text)
        # Remove markdown code block markers
        cleaned_text = re.sub(r'```json\s*|\s*```', '', response_text)
        # Remove any control characters
        cleaned_text = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', cleaned_text)

        try:
            response_data = json.loads(cleaned_text)
            if 'response' in response_data:
                response_data = response_data['response']
            print("Parsed response:", response_data)  # Debug print

            # Ensure response_data is a list of objects and validate each mapping
            if isinstance(response_data, list):
                # Validate and clean mappings
                cleaned_mappings = []
                for mapping in response_data:
                    if all(k in mapping for k in ['source_statement', 'target_statement', 'source_line_number', 'target_line_number', 'status']):
                        # Ensure target-only entries are properly formatted
                        if mapping['status'] == 'Target Only':
                            mapping['source_statement'] = ''
                            mapping['source_line_number'] = '-'
                        cleaned_mappings.append(mapping)

                print("Total mappings:", len(cleaned_mappings))  # Debug: Print count of mappings
                return cleaned_mappings
            else:
                raise ValueError("Unexpected response format: Expected a list of objects.")
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {str(e)}")
            print("Failed to parse text:", cleaned_text)
            raise

    def save_to_csv(self, mappings: List[Dict], output_file: str):
        """Save the mappings to a CSV file."""
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Source Statement', 'Target Statement', 'Source Statement Number',
                           'Target Statement Number', 'Status'])

            for mapping in mappings:
                writer.writerow([
                    mapping['source_statement'],
                    mapping['target_statement'],
                    mapping['source_line_number'],
                    mapping['target_line_number'] if mapping['target_line_number'] != '-' else '-',
                    mapping['status']
                ])

def main():
    # Azure OpenAI configuration
    api_key = "wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm"
    azure_endpoint = "https://ai-testgeneration707727059630.openai.azure.com/"
    deployment_name = "gpt4-deployment"  # The name you gave to your deployed model

    # Initialize analyzer
    analyzer = SQLStatementAnalyzer(api_key, azure_endpoint, deployment_name)

    # Read source and target SQL files
    with open('source.sql', 'r') as f:
        source_content = f.read()
    with open('target.sql', 'r') as f:
        target_content = f.read()

    # Generate mappings
    mappings = analyzer.analyze_sql_files(source_content, target_content)

    # Save to CSV
    analyzer.save_to_csv(mappings, 'azure_sql_mappings.csv')
    print('File Saved')

if __name__ == "__main__":
    main()
