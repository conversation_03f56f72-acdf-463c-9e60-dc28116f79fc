trigger:
- main

resources:
- repo: self

variables:
  dockerRegistryServiceConnection: 'qmigaiassessment'
  imageRepository: 'qmigaiassessment'
  containerRegistry: 'qmigai.azurecr.io'
  dockerfilePath: '**/Dockerfile_QM_Test'
  tag: '$(Build.BuildId)'
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: Docker@2
      displayName: Build
      inputs:
        command: build
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
      
    
    - task: Docker@2
      displayName: push an image to container registry
      inputs:
        command: Push
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
