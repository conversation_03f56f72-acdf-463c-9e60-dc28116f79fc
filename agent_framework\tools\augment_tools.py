"""
Augment-like Tools - Advanced code understanding and manipulation tools
"""
import os
import re
import ast
import json
import logging
from typing import List, Dict, Any, Optional, Union, Tuple
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ===== Code Understanding Tools =====

def analyze_code_deeply(file_path: str) -> str:
    """
    Perform deep code analysis similar to Augment's code understanding

    Args:
        file_path: Path to the code file to analyze

    Returns:
        Detailed analysis of the code structure, symbols, and relationships
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File {file_path} not found"

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            code = f.read()

        # Get file extension
        _, ext = os.path.splitext(file_path)

        # Analyze based on file type
        if ext.lower() == '.py':
            return _analyze_python_code(code, file_path)
        elif ext.lower() in ['.js', '.ts', '.jsx', '.tsx']:
            return _analyze_javascript_code(code, file_path)
        elif ext.lower() in ['.java']:
            return _analyze_java_code(code, file_path)
        elif ext.lower() in ['.c', '.cpp', '.h', '.hpp']:
            return _analyze_cpp_code(code, file_path)
        else:
            return _analyze_generic_code(code, file_path)

    except Exception as e:
        logger.error(f"Error analyzing code: {str(e)}")
        return f"Error analyzing code: {str(e)}"

def _analyze_python_code(code: str, file_path: str) -> str:
    """Analyze Python code structure"""
    try:
        # Parse the AST
        tree = ast.parse(code)

        # Extract information
        imports = []
        classes = []
        functions = []
        global_vars = []

        for node in ast.walk(tree):
            # Get imports
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                if isinstance(node, ast.Import):
                    for name in node.names:
                        imports.append(name.name)
                else:  # ImportFrom
                    module = node.module or ''
                    for name in node.names:
                        imports.append(f"{module}.{name.name}" if module else name.name)

            # Get classes
            elif isinstance(node, ast.ClassDef):
                methods = []
                class_vars = []

                # Get base classes
                bases = [base.id if isinstance(base, ast.Name) else "complex_base"
                         for base in node.bases]

                # Get class methods and variables
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        methods.append({
                            'name': item.name,
                            'args': [arg.arg for arg in item.args.args],
                            'line': item.lineno
                        })
                    elif isinstance(item, ast.Assign):
                        for target in item.targets:
                            if isinstance(target, ast.Name):
                                class_vars.append(target.id)

                classes.append({
                    'name': node.name,
                    'bases': bases,
                    'methods': methods,
                    'variables': class_vars,
                    'line': node.lineno
                })

            # Get functions
            elif isinstance(node, ast.FunctionDef) and node.parent_field != 'body':
                functions.append({
                    'name': node.name,
                    'args': [arg.arg for arg in node.args.args],
                    'line': node.lineno
                })

            # Get global variables
            elif isinstance(node, ast.Assign) and node.parent_field == 'body':
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        global_vars.append(target.id)

        # Format the results
        result = f"# Deep Analysis of {os.path.basename(file_path)}\n\n"

        result += "## Imports\n"
        for imp in imports:
            result += f"- {imp}\n"

        result += "\n## Classes\n"
        for cls in classes:
            result += f"- **{cls['name']}**"
            if cls['bases']:
                result += f" (inherits from: {', '.join(cls['bases'])})"
            result += f" - Line {cls['line']}\n"

            if cls['methods']:
                result += "  - Methods:\n"
                for method in cls['methods']:
                    args_str = ", ".join(method['args'])
                    result += f"    - `{method['name']}({args_str})` - Line {method['line']}\n"

            if cls['variables']:
                result += "  - Class Variables:\n"
                for var in cls['variables']:
                    result += f"    - `{var}`\n"

        result += "\n## Functions\n"
        for func in functions:
            args_str = ", ".join(func['args'])
            result += f"- `{func['name']}({args_str})` - Line {func['line']}\n"

        if global_vars:
            result += "\n## Global Variables\n"
            for var in global_vars:
                result += f"- `{var}`\n"

        # Add relationships section
        result += "\n## Relationships and Dependencies\n"
        # Identify potential relationships between classes and functions
        for cls in classes:
            for method in cls['methods']:
                # Check if method calls other functions or uses classes
                result += f"- Class `{cls['name']}` method `{method['name']}` may interact with other components\n"

        return result

    except SyntaxError as e:
        return f"Python syntax error in {file_path}: {str(e)}"
    except Exception as e:
        return f"Error analyzing Python code: {str(e)}"

def _analyze_javascript_code(code: str, file_path: str) -> str:
    """Analyze JavaScript/TypeScript code structure"""
    # Simple regex-based analysis for JS/TS
    classes = re.findall(r'class\s+(\w+)(?:\s+extends\s+(\w+))?\s*{', code)
    functions = re.findall(r'function\s+(\w+)\s*\(([^)]*)\)', code)
    arrow_funcs = re.findall(r'const\s+(\w+)\s*=\s*(?:\(([^)]*)\)|(\w+))\s*=>', code)
    imports = re.findall(r'import\s+{([^}]+)}\s+from\s+[\'"]([^\'"]+)[\'"]', code)

    result = f"# Deep Analysis of {os.path.basename(file_path)}\n\n"

    result += "## Imports\n"
    for imp in imports:
        modules = [m.strip() for m in imp[0].split(',')]
        for module in modules:
            result += f"- {module} from '{imp[1]}'\n"

    result += "\n## Classes\n"
    for cls in classes:
        if cls[1]:  # Has parent class
            result += f"- **{cls[0]}** (extends {cls[1]})\n"
        else:
            result += f"- **{cls[0]}**\n"

    result += "\n## Functions\n"
    for func in functions:
        args_str = func[1].strip()
        result += f"- `{func[0]}({args_str})`\n"

    for func in arrow_funcs:
        args_str = func[1] if func[1] else func[2] if func[2] else ''
        result += f"- `{func[0]}({args_str})` (arrow function)\n"

    return result

def _analyze_java_code(code: str, file_path: str) -> str:
    """Analyze Java code structure"""
    # Simple regex-based analysis for Java
    package = re.search(r'package\s+([\w.]+);', code)
    imports = re.findall(r'import\s+([\w.]+);', code)
    classes = re.findall(r'(?:public|private|protected)?\s*(?:static)?\s*class\s+(\w+)(?:\s+extends\s+(\w+))?(?:\s+implements\s+([^{]+))?', code)
    methods = re.findall(r'(?:public|private|protected)?\s*(?:static)?\s*(?:final)?\s*(?:[\w<>[\],\s]+)\s+(\w+)\s*\(([^)]*)\)', code)

    result = f"# Deep Analysis of {os.path.basename(file_path)}\n\n"

    if package:
        result += f"## Package\n- {package.group(1)}\n\n"

    result += "## Imports\n"
    for imp in imports:
        result += f"- {imp}\n"

    result += "\n## Classes\n"
    for cls in classes:
        result += f"- **{cls[0]}**"
        if cls[1]:  # Has parent class
            result += f" (extends {cls[1]})"
        if cls[2]:  # Implements interfaces
            interfaces = [i.strip() for i in cls[2].split(',')]
            result += f" (implements {', '.join(interfaces)})"
        result += "\n"

    result += "\n## Methods\n"
    for method in methods:
        args_str = method[1].strip()
        result += f"- `{method[0]}({args_str})`\n"

    return result

def _analyze_cpp_code(code: str, file_path: str) -> str:
    """Analyze C/C++ code structure"""
    # Simple regex-based analysis for C/C++
    includes = re.findall(r'#include\s+[<"]([^>"]+)[>"]', code)
    classes = re.findall(r'class\s+(\w+)(?:\s*:\s*(?:public|private|protected)?\s*(\w+))?', code)
    functions = re.findall(r'(?:[\w:*&]+)\s+(\w+)\s*\(([^)]*)\)\s*(?:const)?\s*(?:{|;)', code)

    result = f"# Deep Analysis of {os.path.basename(file_path)}\n\n"

    result += "## Includes\n"
    for inc in includes:
        result += f"- {inc}\n"

    result += "\n## Classes\n"
    for cls in classes:
        if cls[1]:  # Has parent class
            result += f"- **{cls[0]}** (inherits from {cls[1]})\n"
        else:
            result += f"- **{cls[0]}**\n"

    result += "\n## Functions\n"
    for func in functions:
        args_str = func[1].strip()
        result += f"- `{func[0]}({args_str})`\n"

    return result

def _analyze_generic_code(code: str, file_path: str) -> str:
    """Analyze generic code structure"""
    # Count lines, identify potential functions/classes with simple regex
    lines = code.split('\n')
    line_count = len(lines)

    # Try to identify potential functions or classes with a generic approach
    potential_functions = re.findall(r'(?:function|def|void|int|string|bool|var|let|const)\s+(\w+)\s*\(', code)
    potential_classes = re.findall(r'(?:class|interface|struct|type)\s+(\w+)', code)

    result = f"# Basic Analysis of {os.path.basename(file_path)}\n\n"
    result += f"- File type: {os.path.splitext(file_path)[1]}\n"
    result += f"- Line count: {line_count}\n"

    if potential_classes:
        result += "\n## Potential Classes/Types\n"
        for cls in potential_classes:
            result += f"- {cls}\n"

    if potential_functions:
        result += "\n## Potential Functions/Methods\n"
        for func in potential_functions:
            result += f"- {func}\n"

    return result

# ===== Code Relationship Tools =====

def find_code_relationships(file_path: str, related_paths: Optional[List[str]] = None) -> str:
    """
    Find relationships between code files (imports, dependencies, etc.)

    Args:
        file_path: Primary file to analyze
        related_paths: Optional list of related files to check

    Returns:
        Analysis of code relationships
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File {file_path} not found"

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            primary_code = f.read()

        # Get file extension and directory
        _, ext = os.path.splitext(file_path)
        directory = os.path.dirname(file_path)

        # If no related paths provided, try to find related files in the same directory
        if not related_paths:
            related_paths = []
            for file in os.listdir(directory):
                if file != os.path.basename(file_path) and os.path.splitext(file)[1] == ext:
                    related_paths.append(os.path.join(directory, file))

        # Analyze imports and dependencies
        imports, exports = _extract_imports_exports(primary_code, ext.lower())

        # Check which files import the primary file
        imported_by = []
        for related_path in related_paths:
            try:
                with open(related_path, 'r', encoding='utf-8', errors='ignore') as f:
                    related_code = f.read()

                related_imports, _ = _extract_imports_exports(related_code, os.path.splitext(related_path)[1].lower())
                primary_name = os.path.basename(file_path)
                primary_name_no_ext = os.path.splitext(primary_name)[0]

                # Check if this file imports the primary file
                for imp in related_imports:
                    if primary_name in imp or primary_name_no_ext in imp:
                        imported_by.append(os.path.basename(related_path))
                        break
            except Exception:
                continue

        # Format the results
        result = f"# Code Relationship Analysis for {os.path.basename(file_path)}\n\n"

        result += "## Imports\n"
        if imports:
            for imp in imports:
                result += f"- {imp}\n"
        else:
            result += "- No imports found\n"

        result += "\n## Exports\n"
        if exports:
            for exp in exports:
                result += f"- {exp}\n"
        else:
            result += "- No explicit exports found\n"

        result += "\n## Imported By\n"
        if imported_by:
            for imp in imported_by:
                result += f"- {imp}\n"
        else:
            result += "- Not imported by any of the analyzed files\n"

        return result

    except Exception as e:
        logger.error(f"Error finding code relationships: {str(e)}")
        return f"Error finding code relationships: {str(e)}"

def _extract_imports_exports(code: str, ext: str) -> Tuple[List[str], List[str]]:
    """Extract imports and exports from code based on file type"""
    imports = []
    exports = []

    if ext == '.py':
        # Python imports
        import_matches = re.findall(r'(?:from\s+([\w.]+)\s+import\s+(?:[^#\n]+))|(?:import\s+([\w.,\s]+))', code)
        for match in import_matches:
            if match[0]:  # from X import Y
                imports.append(f"from {match[0]} import ...")
            elif match[1]:  # import X
                modules = [m.strip() for m in match[1].split(',')]
                imports.extend(modules)

        # Python exports (functions, classes)
        exports.extend(re.findall(r'def\s+([^\s(]+)', code))
        exports.extend(re.findall(r'class\s+([^\s:(]+)', code))

    elif ext in ['.js', '.ts', '.jsx', '.tsx']:
        # JS/TS imports
        imports.extend(re.findall(r'import\s+(?:{[^}]+}|[^{}\s]+)\s+from\s+[\'"]([^\'"]+)[\'"]', code))
        imports.extend(re.findall(r'require\([\'"]([^\'"]+)[\'"]\)', code))

        # JS/TS exports
        exports.extend(re.findall(r'export\s+(?:default\s+)?(?:function|class|const|let|var)\s+([^\s(=]+)', code))
        export_objects = re.findall(r'export\s+{([^}]+)}', code)
        for obj in export_objects:
            exports.extend([o.strip() for o in obj.split(',')])

    elif ext in ['.java']:
        # Java imports
        imports.extend(re.findall(r'import\s+([\w.]+);', code))

        # Java exports (public classes, interfaces)
        exports.extend(re.findall(r'public\s+(?:class|interface|enum)\s+(\w+)', code))

    elif ext in ['.c', '.cpp', '.h', '.hpp']:
        # C/C++ includes
        imports.extend(re.findall(r'#include\s+[<"]([^>"]+)[>"]', code))

        # C/C++ exports (functions, classes, structs)
        exports.extend(re.findall(r'(?:class|struct)\s+(\w+)', code))
        exports.extend(re.findall(r'(?:[\w*]+)\s+(\w+)\s*\([^)]*\)\s*(?:{|;)', code))

    return imports, exports

# ===== Planning Tools =====

def plan_changes(file_path: str, change_description: str) -> str:
    """
    Plan changes to a file based on a description

    Args:
        file_path: Path to the file to modify
        change_description: Description of the changes to make

    Returns:
        Detailed plan for making the changes
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File {file_path} not found"

        # Read the file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # Analyze the file structure
        structure = _analyze_file_structure(file_path, content, ext)

        # Generate a plan
        result = f"# Change Plan for {os.path.basename(file_path)}\n\n"
        result += f"## Requested Changes\n{change_description}\n\n"

        result += "## File Structure\n"
        if structure.get('imports'):
            result += "### Imports\n"
            for imp in structure['imports'][:5]:
                result += f"- {imp}\n"
            if len(structure['imports']) > 5:
                result += f"- ... ({len(structure['imports']) - 5} more)\n"

        if structure.get('classes'):
            result += "\n### Classes\n"
            for cls in structure['classes']:
                result += f"- **{cls['name']}**"
                if cls.get('methods'):
                    result += f" (methods: {len(cls['methods'])})"
                result += "\n"

        if structure.get('functions'):
            result += "\n### Functions\n"
            for func in structure['functions'][:5]:
                result += f"- {func}\n"
            if len(structure['functions']) > 5:
                result += f"- ... ({len(structure['functions']) - 5} more)\n"

        # Generate a step-by-step plan
        result += "\n## Change Plan\n"
        result += "1. **Backup the file** before making changes\n"
        result += "2. **Identify the specific sections** that need to be modified\n"

        # Add more specific steps based on the change description
        if "add" in change_description.lower():
            result += "3. **Add new code** as described\n"
            if structure.get('classes'):
                result += "4. **Ensure proper integration** with existing classes\n"
            if structure.get('imports'):
                result += "5. **Update imports** if necessary\n"
        elif "remove" in change_description.lower():
            result += "3. **Identify code to remove** carefully\n"
            result += "4. **Check for dependencies** before removing\n"
            result += "5. **Remove unused imports** if applicable\n"
        elif "modify" in change_description.lower() or "change" in change_description.lower():
            result += "3. **Locate the specific code** to modify\n"
            result += "4. **Make the requested changes** while preserving structure\n"
            result += "5. **Update related code** that might be affected\n"

        result += "6. **Test the changes** to ensure functionality\n"
        result += "7. **Review the changes** for any issues\n"

        return result

    except Exception as e:
        error_msg = f"Error planning changes: {str(e)}"
        logger.error(error_msg)
        return error_msg

def _analyze_file_structure(file_path: str, content: str, ext: str) -> Dict[str, Any]:
    """Analyze the structure of a file"""
    structure = {
        'imports': [],
        'classes': [],
        'functions': []
    }

    # Python structure
    if ext == '.py':
        # Extract imports
        import_pattern = r'^\s*(?:from\s+[\w.]+\s+import|import\s+[\w.,\s]+)'
        structure['imports'] = [m.group(0).strip() for m in re.finditer(import_pattern, content, re.MULTILINE)]

        # Extract classes
        class_pattern = r'^\s*class\s+(\w+)'
        classes = []
        for match in re.finditer(class_pattern, content, re.MULTILINE):
            class_name = match.group(1)

            # Find methods in this class
            class_start = match.start()
            next_class = re.search(class_pattern, content[class_start + 1:], re.MULTILINE)
            class_end = next_class.start() + class_start + 1 if next_class else len(content)

            class_content = content[class_start:class_end]
            method_pattern = r'^\s+def\s+(\w+)'
            methods = [m.group(1) for m in re.finditer(method_pattern, class_content, re.MULTILINE)]

            classes.append({
                'name': class_name,
                'methods': methods
            })

        structure['classes'] = classes

        # Extract functions
        function_pattern = r'^\s*def\s+(\w+)'
        structure['functions'] = [m.group(1) for m in re.finditer(function_pattern, content, re.MULTILINE)]

    # JavaScript/TypeScript structure
    elif ext in ['.js', '.ts', '.jsx', '.tsx']:
        # Extract imports
        import_pattern = r'^\s*(?:import\s+.*\s+from\s+[\'"][^\'"]+[\'"]|const\s+.*\s+=\s+require\([\'"][^\'"]+[\'"]\))'
        structure['imports'] = [m.group(0).strip() for m in re.finditer(import_pattern, content, re.MULTILINE)]

        # Extract classes
        class_pattern = r'^\s*class\s+(\w+)'
        classes = []
        for match in re.finditer(class_pattern, content, re.MULTILINE):
            class_name = match.group(1)

            # Find methods in this class
            class_start = match.start()
            next_class = re.search(class_pattern, content[class_start + 1:], re.MULTILINE)
            class_end = next_class.start() + class_start + 1 if next_class else len(content)

            class_content = content[class_start:class_end]
            method_pattern = r'^\s+(?:async\s+)?(\w+)\s*\('
            methods = [m.group(1) for m in re.finditer(method_pattern, class_content, re.MULTILINE)]

            classes.append({
                'name': class_name,
                'methods': methods
            })

        structure['classes'] = classes

        # Extract functions
        function_patterns = [
            r'^\s*function\s+(\w+)',
            r'^\s*const\s+(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>'
        ]

        functions = []
        for pattern in function_patterns:
            functions.extend([m.group(1) for m in re.finditer(pattern, content, re.MULTILINE)])

        structure['functions'] = functions

    return structure

# ===== IDE Integration Tools =====

def diagnostics(paths: Optional[List[str]] = None) -> str:
    """
    Get issues (errors, warnings, etc.) from files

    Args:
        paths: Optional list of file paths to check

    Returns:
        Diagnostic information in markdown format
    """
    try:
        # If no paths provided, use current directory
        if not paths:
            paths = [os.getcwd()]

        result = "# Code Diagnostics\n\n"

        for path in paths:
            if not os.path.exists(path):
                result += f"Error: Path not found: {path}\n\n"
                continue

            if os.path.isdir(path):
                # Scan directory for files to check
                files_to_check = []
                for root, _, files in os.walk(path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        _, ext = os.path.splitext(file_path)

                        # Only check code files
                        if ext.lower() in ['.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.c', '.cpp', '.h']:
                            files_to_check.append(file_path)
            else:
                files_to_check = [path]

            # Check each file
            issues_found = False
            for file_path in files_to_check:
                file_issues = _check_file_issues(file_path)

                if file_issues:
                    issues_found = True
                    result += f"## Issues in {os.path.relpath(file_path)}\n"

                    for issue in file_issues:
                        result += f"- **{issue['type']}** at line {issue['line']}: {issue['message']}\n"

                    result += "\n"

            if not issues_found:
                result += "No issues found in the specified paths.\n"

        return result

    except Exception as e:
        error_msg = f"Error running diagnostics: {str(e)}"
        logger.error(error_msg)
        return error_msg

def _check_file_issues(file_path: str) -> List[Dict[str, Any]]:
    """Check a file for issues"""
    issues = []

    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # Check for common issues based on file type
        if ext == '.py':
            # Check for syntax errors
            try:
                import ast
                ast.parse(content)
            except SyntaxError as e:
                issues.append({
                    'type': 'Syntax Error',
                    'line': e.lineno,
                    'message': str(e)
                })

            # Check for common issues
            lines = content.splitlines()
            for i, line in enumerate(lines):
                # Check for TODO comments
                if 'TODO' in line or 'FIXME' in line:
                    issues.append({
                        'type': 'TODO',
                        'line': i + 1,
                        'message': line.strip()
                    })

                # Check for print statements (often left in for debugging)
                if re.match(r'^\s*print\s*\(', line):
                    issues.append({
                        'type': 'Debug Code',
                        'line': i + 1,
                        'message': 'Print statement may be left from debugging'
                    })

                # Check for very long lines
                if len(line) > 100:
                    issues.append({
                        'type': 'Style',
                        'line': i + 1,
                        'message': 'Line exceeds 100 characters'
                    })

        elif ext in ['.js', '.ts', '.jsx', '.tsx']:
            # Check for common issues
            lines = content.splitlines()
            for i, line in enumerate(lines):
                # Check for TODO comments
                if 'TODO' in line or 'FIXME' in line:
                    issues.append({
                        'type': 'TODO',
                        'line': i + 1,
                        'message': line.strip()
                    })

                # Check for console.log statements
                if re.match(r'^\s*console\.log\s*\(', line):
                    issues.append({
                        'type': 'Debug Code',
                        'line': i + 1,
                        'message': 'Console.log statement may be left from debugging'
                    })

                # Check for very long lines
                if len(line) > 100:
                    issues.append({
                        'type': 'Style',
                        'line': i + 1,
                        'message': 'Line exceeds 100 characters'
                    })

    except Exception as e:
        logger.warning(f"Error checking file {file_path}: {str(e)}")

    return issues

def analyze_dependencies(file_path: str, max_depth: int = 2) -> str:
    """
    Analyze dependencies of a file (imports, requires, etc.)

    Args:
        file_path: Path to the file to analyze
        max_depth: Maximum depth for dependency analysis

    Returns:
        Dependency analysis in markdown format
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File {file_path} not found"

        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # Read the file
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Extract direct dependencies
        dependencies = _extract_dependencies(file_path, content, ext)

        # Format the results
        result = f"# Dependency Analysis for {os.path.basename(file_path)}\n\n"
        result += "## Direct Dependencies\n"

        if not dependencies:
            result += "No direct dependencies found.\n"
        else:
            for dep in dependencies:
                result += f"- {dep['name']}"
                if dep.get('path'):
                    result += f" (from {dep['path']})"
                result += "\n"

        # Analyze transitive dependencies if depth > 1
        if max_depth > 1 and dependencies:
            result += "\n## Transitive Dependencies\n"

            transitive_deps = []
            for dep in dependencies:
                if dep.get('path') and os.path.exists(dep['path']):
                    try:
                        with open(dep['path'], 'r', encoding='utf-8', errors='ignore') as f:
                            dep_content = f.read()

                        # Get file extension
                        _, dep_ext = os.path.splitext(dep['path'])
                        dep_ext = dep_ext.lower()

                        # Extract dependencies
                        dep_deps = _extract_dependencies(dep['path'], dep_content, dep_ext)

                        if dep_deps:
                            result += f"\n### Dependencies of {os.path.basename(dep['path'])}\n"
                            for d in dep_deps:
                                result += f"- {d['name']}"
                                if d.get('path'):
                                    result += f" (from {d['path']})"
                                result += "\n"
                                transitive_deps.append(d)
                    except Exception as e:
                        logger.warning(f"Error analyzing transitive dependency {dep['path']}: {str(e)}")

            if not transitive_deps:
                result += "No transitive dependencies found.\n"

        return result

    except Exception as e:
        error_msg = f"Error analyzing dependencies: {str(e)}"
        logger.error(error_msg)
        return error_msg

def _extract_dependencies(file_path: str, content: str, ext: str) -> List[Dict[str, Any]]:
    """Extract dependencies from file content based on file type"""
    dependencies = []

    # Python dependencies
    if ext == '.py':
        # Extract imports
        import_patterns = [
            r'^\s*import\s+([\w.]+)',  # import module
            r'^\s*from\s+([\w.]+)\s+import'  # from module import ...
        ]

        for pattern in import_patterns:
            for match in re.finditer(pattern, content, re.MULTILINE):
                module = match.group(1)

                # Skip standard library modules
                if module in ['os', 'sys', 're', 'json', 'time', 'datetime', 'logging', 'math', 'random']:
                    continue

                # Try to find the module path
                module_path = _find_module_path(module)

                dependencies.append({
                    'name': module,
                    'type': 'import',
                    'path': module_path
                })

    # JavaScript/TypeScript dependencies
    elif ext in ['.js', '.ts', '.jsx', '.tsx']:
        # Extract imports
        import_patterns = [
            r'^\s*import\s+.*\s+from\s+[\'"]([^\'"]+)[\'"]',  # import ... from 'module'
            r'^\s*const\s+.*\s+=\s+require\([\'"]([^\'"]+)[\'"]\)'  # const ... = require('module')
        ]

        for pattern in import_patterns:
            for match in re.finditer(pattern, content, re.MULTILINE):
                module = match.group(1)

                # Skip relative imports that start with .
                if module.startswith('.'):
                    # Try to resolve the relative path
                    module_path = os.path.normpath(os.path.join(os.path.dirname(file_path), module))

                    # Add common extensions if not specified
                    if not os.path.exists(module_path):
                        for ext in ['.js', '.ts', '.jsx', '.tsx']:
                            if os.path.exists(module_path + ext):
                                module_path = module_path + ext
                                break
                else:
                    module_path = None

                dependencies.append({
                    'name': module,
                    'type': 'import',
                    'path': module_path
                })

    # Java dependencies
    elif ext == '.java':
        # Extract imports
        import_pattern = r'^\s*import\s+([\w.]+);'

        for match in re.finditer(import_pattern, content, re.MULTILINE):
            module = match.group(1)

            dependencies.append({
                'name': module,
                'type': 'import',
                'path': None  # Java module resolution is complex
            })

    return dependencies

def _find_module_path(module: str) -> Optional[str]:
    """Try to find the path of a Python module"""
    try:
        # Try common locations
        common_paths = [
            os.path.join(os.getcwd(), module.replace('.', os.sep) + '.py'),
            os.path.join(os.getcwd(), module.split('.')[0], '__init__.py')
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return None
    except Exception:
        return None
