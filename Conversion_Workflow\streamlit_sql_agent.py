import streamlit as st
from langchain_openai import AzureChatOpenAI
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import Chat<PERSON><PERSON><PERSON><PERSON><PERSON>plate, MessagesPlaceholder
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel, <PERSON>
from typing import Optional, List

# Set page configuration
st.set_page_config(
    page_title="SQL Database Agent",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Define a Pydantic model for structured SQL responses
class SQLResponse(BaseModel):
    """Model for SQL agent responses."""
    answer: str = Field(
        description="The main answer to the user's question in a clear, concise format"
    )
    details: Optional[str] = Field(
        default=None,
        description="Additional explanation or context about the answer"
    )
    results: Optional[str] = Field(
        default=None,
        description="Formatted query results in a readable format"
    )
    sql_code: Optional[str] = Field(
        default=None,
        description="SQL code that was generated or corrected as part of the response"
    )
    recommendations: Optional[List[str]] = Field(
        default=None,
        description="Optional recommendations for the user based on their query"
    )

# Initialize session state for chat history if it doesn't exist
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []

# Initialize session state for SQL code
if "sql_code" not in st.session_state:
    st.session_state.sql_code = None

# Initialize the LLM
@st.cache_resource
def initialize_agent():
    """Initialize the SQL agent with database connection and tools."""

    # Initialize the LLM with Azure OpenAI
    llm = AzureChatOpenAI(
        azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
        azure_deployment="gpt4-deployment",
        api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
        api_version="2024-12-01-preview",
        temperature=0.3,
    )

    # Create a SQLDatabase connection - using the exact same connection string
    db = SQLDatabase.from_uri('postgresql+psycopg2://postgres:postgres@localhost/postgres')

    # Initialize the SQLDatabaseToolkit
    toolkit = SQLDatabaseToolkit(db=db, llm=llm)

    # Define custom tools
    @tool
    def get_database_size() -> dict:
        """Get the total size of the database.

        This tool returns the size of the database in megabytes.
        Use this to check database storage usage.

        Returns:
            A dictionary with the database size information
        """
        # Use the existing db connection
        result = db.run("""
            SELECT
                pg_size_pretty(pg_database_size(current_database())) as size,
                pg_database_size(current_database()) as size_bytes
        """)
        return result

    @tool
    def get_database_tables():
        """Get the list of tables in the database.

        This tool directly connects to the database and returns all table names.
        Use this when you need a simple list of tables without additional schema information.

        Returns:
            A list of dictionaries containing table names
        """
        # Use the existing db connection
        result = db.run("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_type = 'BASE TABLE'
            AND table_schema NOT IN ('pg_catalog', 'information_schema');
        """)
        return result

    @tool
    def get_table_row_count(table_name: str) -> dict:
        """Get the number of rows in a table.

        This tool returns the row count for a specific table.
        Use this to check the number of records in a table.

        Args:
            table_name: The name of the table to check

        Returns:
            A dictionary with the table name and row count
        """
        # Use the existing db connection
        result = db.run(f"SELECT COUNT(*) FROM {table_name}")
        return result

    # Define custom tools
    custom_tools = [get_database_size, get_database_tables, get_table_row_count]

    # Get all tools from the SQLDatabaseToolkit
    sql_tools = toolkit.get_tools()
    st.write(f"SQL toolkit tools: {len(sql_tools)} tools available")

    # Combine with custom tools
    all_tools = sql_tools + custom_tools
    st.write(f"Combined tools: {len(all_tools)} tools available")
    st.write(f"Tool names: {[tool.name for tool in all_tools]}")

    # Create a system prompt for the LLM with SQL code extraction
    system_message = f"""
    You are an expert SQL database analyst with access to a PostgreSQL database.
    Your job is to help users understand the database structure, run queries, and analyze data.

    IMPORTANT: You have direct access to the database through several tools. Always use the appropriate tools to answer questions about the database.

    When answering questions, follow these guidelines:

    1. ANSWER: Always provide a clear, direct answer to the user's question in 1-3 sentences.

    2. DETAILS: When helpful, include additional context, explanations, or analysis of the database structure or query results.

    3. SQL_CODE: When you generate, modify, or correct SQL code, always include the complete SQL code in a dedicated section. Format it using SQL code blocks with triple backticks.

    4. RESULTS: Present query results in a well-formatted way. Use markdown tables for structured data when appropriate.

    5. RECOMMENDATIONS: When relevant, suggest 1-3 follow-up queries or actions the user might want to take.

    Format your responses for maximum readability:
    - Use markdown formatting for headers, lists, and emphasis
    - Present tables with clear headers and aligned columns
    - Always put SQL code in ```sql code blocks
    - Highlight important information
    - Maintain conversation context by referring to previous queries when relevant
    """

    # Create a chat prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_message),
        MessagesPlaceholder(variable_name="chat_history", optional=True),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad")
    ])

    # Bind tools to the LLM - exactly as in db_tools.py
    llm_with_tools = llm.bind_tools(all_tools)

    # Create an agent with the tools
    agent = create_openai_tools_agent(llm_with_tools, all_tools, prompt)

    # Create the agent executor with verbose output to capture SQL queries
    agent_executor = AgentExecutor(
        agent=agent,
        tools=all_tools,
        verbose=True,
        handle_parsing_errors=True,
        max_iterations=10,
        return_intermediate_steps=True  # This will return the intermediate steps including SQL queries
    )

    # Create a structured output version of the LLM
    structured_llm = llm.with_structured_output(SQLResponse)

    return agent_executor, structured_llm

def main():
    """Main function to run the Streamlit app."""

    st.title("🔍 SQL Database Agent")
    st.markdown("Ask questions about your database in natural language.")

    # Initialize the agent
    agent_executor, structured_llm = initialize_agent()

    # Sidebar with example queries - including SQL code examples
    st.sidebar.title("Example Queries")
    example_queries = [
        "List all tables in the database",
        "Show me the schema of the admin_key_areas table",
        "How many rows are in each table?",
        "What is the total size of the database?",
        "Find all records in user_docs where id = 1",
        "What are the largest tables in the database?",
        "Show me the first 5 rows from user_docs",
        "Analyze the performance of the database"
    ]

    # Add SQL code examples
    st.sidebar.title("SQL Code Examples")
    sql_examples = [
        "Fix this SQL code: SELECT * FROM users WHERE id = 1 LIMIT",
        "Create a stored procedure to update user information",
        "Write a query to find duplicate records in a table",
        "Optimize this query: SELECT * FROM large_table WHERE created_date > '2023-01-01'",
        "Fix the mismatched parentheses in this SQL code: RAISE NOTICE '%', LENGTH(TRIM(v_SubDataType_List)",
    ]

    for query in sql_examples:
        if st.sidebar.button(query, key=f"sql_{query[:20]}"):
            # Set the query as the user input
            st.session_state.user_input = query
            st.rerun()

    for query in example_queries:
        if st.sidebar.button(query):
            # Set the query as the user input
            st.session_state.user_input = query
            st.rerun()

    # Clear chat button
    if st.sidebar.button("Clear Chat"):
        st.session_state.chat_history = []
        st.rerun()

    # User input
    user_input = st.chat_input("Enter your database query...")

    # Display chat history
    for message in st.session_state.chat_history:
        if message["role"] == "user":
            st.markdown(f"<div style='background-color: #e6f7ff; padding: 10px; border-radius: 5px; margin-bottom: 10px;'><strong>You:</strong> {message['content']}</div>", unsafe_allow_html=True)
        else:
            st.markdown(f"<div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 10px;'><strong>SQL Agent:</strong> {message['content']}</div>", unsafe_allow_html=True)

    # Check if there's input from the example buttons
    if "user_input" in st.session_state and st.session_state.user_input:
        user_input = st.session_state.user_input
        st.session_state.user_input = None

    if user_input:
        # Add user message to chat history (but don't display it yet since we'll display it separately)
        st.session_state.chat_history.append({"role": "user", "content": user_input})

        # Force a rerun to display the updated chat history with the new user message
        st.rerun()

    # Check if we need to process a new message (the last message in history is from user and has no response yet)
    if (st.session_state.chat_history and
        st.session_state.chat_history[-1]["role"] == "user" and
        (len(st.session_state.chat_history) == 1 or
         st.session_state.chat_history[-2]["role"] == "assistant")):

        # Get the last user message
        last_user_message = st.session_state.chat_history[-1]["content"]

        # Convert session state chat history to LangChain message format
        langchain_messages = []
        for message in st.session_state.chat_history:
            if message["role"] == "user":
                langchain_messages.append(HumanMessage(content=message["content"]))
            else:
                langchain_messages.append(AIMessage(content=message["content"]))

        # Show a spinner while processing
        with st.spinner("Processing your query..."):
            # Get raw response from the agent
            raw_response = agent_executor.invoke({
                "input": last_user_message,
                "chat_history": langchain_messages[:-1]  # Exclude the current user message
            })

            # Use structured LLM to format the response nicely
            structured_prompt = f"""
            Based on the following database query and response, provide a structured answer:

            USER QUERY: {last_user_message}

            RAW RESPONSE: {raw_response["output"]}

            IMPORTANT INSTRUCTIONS FOR SQL CODE:
            1. If the response contains SQL code (usually in ```sql code blocks), extract it and include it in the sql_code field.
            2. Make sure to include the complete SQL code without any formatting or markdown.
            3. If there are multiple SQL code blocks, combine them into a single coherent SQL script.
            4. Remove any line numbers or other non-SQL content from the code.
            5. If the user is asking for SQL code or a fix to SQL code, this is high priority - make sure to extract it correctly.
            """

            # Get structured response using the Pydantic model
            structured_response = structured_llm.invoke(structured_prompt)

            # Format the response exactly like in db_tools.py
            formatted_response = ""

            # Add answer
            formatted_response += f"\n\n 📌 ANSWER:\n{structured_response.answer}\n\n"

            # Add details if available
            if structured_response.details:
                formatted_response += f"📋 DETAILS:\n{structured_response.details}\n\n"

            # Add SQL code if available
            if structured_response.sql_code:
                formatted_response += f"💻 SQL CODE:\n```sql\n{structured_response.sql_code}\n```\n\n"
                # Store the SQL code in session state for display
                st.session_state.sql_code = structured_response.sql_code
            else:
                # Try to extract SQL code from results or raw response if not provided directly
                import re

                # First check in results
                if structured_response.results:
                    sql_code_match = re.search(r'```sql\s*([\s\S]*?)\s*```', structured_response.results)
                    if sql_code_match:
                        sql_code = sql_code_match.group(1).strip()
                        st.session_state.sql_code = sql_code
                        formatted_response += f"💻 SQL CODE:\n```sql\n{sql_code}\n```\n\n"
                    else:
                        # Try to find any code block
                        code_block_match = re.search(r'```\w*\s*([\s\S]*?)\s*```', structured_response.results)
                        if code_block_match:
                            sql_code = code_block_match.group(1).strip()
                            st.session_state.sql_code = sql_code
                            formatted_response += f"💻 SQL CODE:\n```sql\n{sql_code}\n```\n\n"

                # If not found in results, check in raw response
                if not hasattr(st.session_state, 'sql_code') or not st.session_state.sql_code:
                    sql_code_match = re.search(r'```sql\s*([\s\S]*?)\s*```', raw_response["output"])
                    if sql_code_match:
                        sql_code = sql_code_match.group(1).strip()
                        st.session_state.sql_code = sql_code
                        formatted_response += f"💻 SQL CODE:\n```sql\n{sql_code}\n```\n\n"

            # Add results if available
            if structured_response.results:
                formatted_response += f"📊 RESULTS:\n{structured_response.results}\n\n"

            # Add recommendations if available
            if structured_response.recommendations:
                formatted_response += "💡 RECOMMENDATIONS:\n"
                for i, rec in enumerate(structured_response.recommendations, 1):
                    formatted_response += f"{i}. {rec}\n"

        # Add AI response to chat history
        st.session_state.chat_history.append({"role": "assistant", "content": formatted_response})

        # Display the AI response immediately
        st.markdown(f"<div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 10px;'><strong>SQL Agent:</strong> {formatted_response}</div>", unsafe_allow_html=True)

        # Display SQL code if available
        if hasattr(st.session_state, 'sql_code') and st.session_state.sql_code:
            st.markdown("---")
            st.markdown("## 💻 Extracted SQL Code")
            st.code(st.session_state.sql_code, language="sql")

            # Add a text area for easy copying
            st.text_area("Copy SQL Code (Ctrl+A to select all, Ctrl+C to copy)", st.session_state.sql_code, height=200)

            # Add a download button for the SQL code
            sql_filename = "extracted_sql_code.sql"
            st.download_button(
                label="Download SQL Code",
                data=st.session_state.sql_code,
                file_name=sql_filename,
                mime="text/plain"
            )

        # Force a rerun to update the UI for the next interaction
        st.rerun()

if __name__ == "__main__":
    main()
