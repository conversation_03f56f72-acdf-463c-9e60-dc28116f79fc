"""
Main Agent Class - Core of the AI Coding Assistant
"""
import json
import logging
from typing import Dict, List, Any, Optional, Callable

from ..tools.tool_registry import ToolRegistry
from ..memory.memory_manager import MemoryManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Agent:
    """
    Main Agent class that orchestrates the workflow between LLM and tools
    """

    def __init__(self, llm_service, tool_registry: Optional[ToolRegistry] = None,
                 memory_manager: Optional[MemoryManager] = None):
        """
        Initialize the Agent with necessary components

        Args:
            llm_service: Service for interacting with the LLM
            tool_registry: Registry of available tools
            memory_manager: System for storing and retrieving memories
        """
        self.llm_service = llm_service
        self.tool_registry = tool_registry or ToolRegistry()
        self.memory_manager = memory_manager or MemoryManager()
        self.conversation_history = []

    def process_request(self, user_input: str) -> str:
        """
        Process a user request and generate a response with enhanced planning

        Args:
            user_input: The user's request text

        Returns:
            Agent's response to the user
        """
        try:
            # Add user message to conversation history
            self.conversation_history.append({"role": "user", "content": user_input})

            # Store important user queries as memories (like Augment does)
            self._maybe_add_memory_from_query(user_input)

            # Get memories that might be relevant
            memories = self.memory_manager.get_relevant_memories(user_input)
            logger.info(f"Found {len(memories)} relevant memories")

            # 1. Information Gathering Phase
            info_gathering_results = self._gather_information(user_input, memories)

            # 2. Planning Phase
            plan = self._create_execution_plan(user_input, info_gathering_results, memories)
            logger.info("Created execution plan")

            # 3. Execution Phase
            if plan.get("requires_tools", False):
                # Execute the plan using tools
                logger.info("Plan requires tool execution")
                final_response = self._execute_plan(plan, user_input)
            else:
                # Simple response without tool execution
                logger.info("Plan does not require tool execution")
                final_response = plan.get("response", "I'm not sure how to respond to that.")

            # Add assistant response to conversation history if not already added
            if not any(msg.get("role") == "assistant" and msg.get("content") == final_response
                    for msg in self.conversation_history[-3:]):  # Check last few messages
                self.conversation_history.append({"role": "assistant", "content": final_response})

            # Store important insights from the response as memories
            self._maybe_add_memory_from_response(user_input, final_response)

            return final_response

        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)
            return f"I encountered an error while processing your request: {str(e)}"

    def _maybe_add_memory_from_query(self, query: str) -> None:
        """
        Analyze user query and potentially add it as a memory

        Args:
            query: The user's query
        """
        # Check if the query contains important information worth remembering

        # 1. Check for preference indicators
        preference_indicators = ["prefer", "want", "like", "don't like", "should", "always", "never"]
        for indicator in preference_indicators:
            if indicator.lower() in query.lower():
                # This looks like a preference, store it
                self.memory_manager.add_memory(
                    content=query,
                    category="preference"
                )
                logger.info(f"Added user preference to memory: {query[:50]}...")
                return

        # 2. Check for code-related queries
        code_indicators = ["how to", "how do I", "what's the best way to", "code for", "implement",
                          "function", "class", "method", "syntax for"]
        for indicator in code_indicators:
            if indicator.lower() in query.lower():
                # This looks like a code question, store it
                self.memory_manager.add_memory(
                    content=query,
                    category="code"
                )
                logger.info(f"Added code-related query to memory: {query[:50]}...")
                return

        # 3. Check for project-specific information
        project_indicators = ["in this project", "for this codebase", "in our system", "our application"]
        for indicator in project_indicators:
            if indicator.lower() in query.lower():
                # This contains project-specific information
                self.memory_manager.add_memory(
                    content=query,
                    category="project"
                )
                logger.info(f"Added project information to memory: {query[:50]}...")
                return

    def _maybe_add_memory_from_response(self, query: str, response: str) -> None:
        """
        Analyze response and potentially add insights as memories

        Args:
            query: The user's query
            response: The agent's response
        """
        # Only store memories from substantial responses
        if len(response) < 100:
            return

        # Extract potential insights from the response
        insights = []

        # Look for explanatory sections that might be worth remembering
        explanation_markers = ["important to note", "key concept", "best practice", "common pattern",
                              "recommended approach"]

        for marker in explanation_markers:
            if marker.lower() in response.lower():
                # Find the sentence containing this marker
                sentences = response.split('. ')
                for sentence in sentences:
                    if marker.lower() in sentence.lower():
                        insights.append(sentence.strip() + '.')

        # Store each insight as a separate memory
        for insight in insights:
            if len(insight) > 20:  # Only store substantial insights
                self.memory_manager.add_memory(
                    content=insight,
                    category="knowledge"
                )
                logger.info(f"Added insight to memory: {insight[:50]}...")

    def _gather_information(self, user_input: str, memories: List[str]) -> List[Dict[str, Any]]:
        """
        Gather necessary information before planning

        Args:
            user_input: The user's request
            memories: Relevant memories

        Returns:
            List of information gathering results
        """
        # Create a specialized prompt for information gathering
        info_gathering_prompt = """
        You are an AI coding assistant tasked with gathering necessary information before planning actions.

        # Task
        Analyze the user's request and determine what information you need to gather before creating a plan.
        Identify specific information needs related to:
        1. Code understanding (specific files, functions, classes)
        2. System state (environment, configurations)
        3. User requirements clarification

        # Output Format
        Provide a list of information gathering actions in the following format:
        - For code retrieval: Use the codebase_retrieval tool
        - For file reading: Use the read_file tool
        - For code analysis: Use the analyze_code_structure tool

        Be specific and thorough in your information gathering.
        """

        # Get tools that can be used for information gathering
        info_gathering_tools = [
            tool for tool in self.tool_registry.get_tools_description()
            if tool["name"] in ["codebase_retrieval", "read_file", "analyze_code_structure", "list_files"]
        ]

        # Generate information gathering plan
        info_plan_response = self.llm_service.generate_response(
            system_prompt=info_gathering_prompt,
            tools=info_gathering_tools,
            memories=memories,
            user_input=user_input
        )

        # Extract tool calls for information gathering
        info_tool_calls = self._extract_tool_calls(info_plan_response)

        # Execute information gathering tools
        info_results = []

        if info_tool_calls:
            for tool_call in info_tool_calls:
                tool_id = tool_call.get("id", f"info-{len(info_results)}")
                tool_name = tool_call.get("name")
                parameters = tool_call.get("parameters", {})

                logger.info(f"Gathering information with tool: {tool_name}")

                if not self.tool_registry.has_tool(tool_name):
                    info_results.append({
                        "tool_id": tool_id,
                        "tool_name": tool_name,
                        "status": "error",
                        "result": f"Tool '{tool_name}' not found"
                    })
                    continue

                try:
                    result = self.tool_registry.execute_tool(tool_name, **parameters)
                    info_results.append({
                        "tool_id": tool_id,
                        "tool_name": tool_name,
                        "status": "success",
                        "result": result
                    })
                except Exception as e:
                    error_msg = f"Error executing info gathering tool {tool_name}: {str(e)}"
                    logger.error(error_msg)
                    info_results.append({
                        "tool_id": tool_id,
                        "tool_name": tool_name,
                        "status": "error",
                        "result": f"Error: {str(e)}"
                    })

        return info_results

    def _create_execution_plan(self, user_input: str, info_results: List[Dict[str, Any]],
                              memories: List[str]) -> Dict[str, Any]:
        """
        Create a detailed execution plan based on gathered information

        Args:
            user_input: The user's request
            info_results: Results from information gathering
            memories: Relevant memories

        Returns:
            Execution plan with steps and tool calls
        """
        # Create a specialized prompt for planning
        planning_prompt = """
        You are an AI coding assistant tasked with creating a detailed execution plan.

        # Task
        Based on the user's request and the information gathered, create a detailed plan for execution.

        # Planning Guidelines
        1. Break down the task into clear, sequential steps
        2. Identify which tools are needed for each step
        3. Specify the exact parameters for each tool call
        4. Consider potential edge cases and error handling
        5. Determine if the task requires code changes or just information retrieval

        # Output Format
        Your response should include:
        1. A summary of what you understand the user is asking for
        2. A detailed plan with numbered steps
        3. For each step that requires a tool, specify the tool name and parameters
        4. If no tools are needed, provide a direct response to the user's request

        Be specific, thorough, and precise in your planning.
        """

        # Format information gathering results
        info_results_str = ""
        for result in info_results:
            info_results_str += f"## {result['tool_name']} Result:\n"
            info_results_str += f"Status: {result['status']}\n"
            info_results_str += f"```\n{result['result']}\n```\n\n"

        # Get all available tools for the planning phase
        all_tools = self.tool_registry.get_tools_description()

        # Generate the execution plan
        plan_response = self.llm_service.generate_response(
            system_prompt=planning_prompt,
            tools=all_tools,
            memories=memories,
            user_input=f"User Request: {user_input}\n\nInformation Gathered:\n{info_results_str}"
        )

        # Extract tool calls from the plan
        plan_tool_calls = self._extract_tool_calls(plan_response)

        # Create the execution plan
        plan = {
            "requires_tools": len(plan_tool_calls) > 0,
            "tool_calls": plan_tool_calls,
            "response": plan_response if isinstance(plan_response, str) else plan_response.get("content", "")
        }

        return plan

    def _execute_plan(self, plan: Dict[str, Any], original_request: str) -> str:
        """
        Execute the plan using tools

        Args:
            plan: The execution plan
            original_request: The original user request

        Returns:
            Final response after execution
        """
        # If the plan has tool calls, execute them
        if plan.get("tool_calls"):
            return self._execute_tool_workflow(plan["tool_calls"], original_request)
        else:
            return plan.get("response", "I'm not sure how to respond to that.")

    def _generate_initial_response(self, user_input: str, memories: List[str]) -> Any:
        """
        Generate initial response or planning from the LLM

        Args:
            user_input: The user's request
            memories: Relevant memories to include

        Returns:
            LLM's initial response (string or dict with tool calls)
        """
        # Create a prompt that includes:
        # 1. The system prompt defining agent behavior
        # 2. Available tools and their descriptions
        # 3. Relevant memories
        # 4. Conversation history
        # 5. Current user request

        system_prompt = self._create_system_prompt()
        tools_description = self.tool_registry.get_tools_description()

        response = self.llm_service.generate_response(
            system_prompt=system_prompt,
            tools=tools_description,
            memories=memories,
            conversation_history=self.conversation_history,
            user_input=user_input
        )

        return response

    def _create_system_prompt(self) -> str:
        """Create the system prompt that defines agent behavior"""
        return """
        You are an advanced AI coding assistant, designed to help users with programming tasks.

        # Role
        You are an agentic coding AI assistant with access to the developer's codebase through powerful tools.
        You can read from and write to the codebase, search for information, and execute commands.
        You are designed to be helpful, accurate, and efficient in solving coding problems.

        # Workflow
        1. Understand the user's request thoroughly by analyzing the specific coding task or question
        2. Gather necessary information about the codebase using the appropriate tools
   - Use codebase_retrieval to find relevant code
   - Use read_file to examine specific files
   - Use analyze_code_structure to understand code organization
        3. Create a detailed plan before making any changes
   - Break down complex tasks into smaller, manageable steps
   - Identify potential challenges or edge cases
   - Determine which files need to be modified
        4. Execute the plan step by step using available tools
   - Make precise, targeted changes to the code
   - Verify each step before proceeding to the next
        5. Verify your work and explain what you've done
   - Test the changes if possible
   - Provide a clear explanation of the changes made
   - Highlight any important considerations or limitations

        # Information Gathering
        Be extremely thorough in your information gathering:
        - Search for relevant code using specific, targeted queries
        - Examine file structures and dependencies
        - Look for similar patterns or implementations in the existing codebase
        - Understand the context and purpose of the code you're working with
        - Identify potential impacts of changes on other parts of the codebase

        # Planning
        Create detailed, precise plans:
        - List each file that needs to be changed
        - Specify the exact changes needed in each file
        - Consider dependencies and potential side effects
        - Plan for error handling and edge cases
        - Break complex changes into smaller, verifiable steps

        # Code Editing
        When making edits:
        - Always first gather detailed information about the code you want to edit
        - Understand all symbols, functions, classes, and variables involved
        - Be very conservative and respect the existing code style and patterns
        - Make minimal changes necessary to accomplish the task
        - Preserve existing functionality unless explicitly asked to change it
        - Add appropriate comments for complex changes

        # Code Quality
        Maintain high code quality:
        - Follow the existing code style and conventions
        - Write clean, readable, and maintainable code
        - Add appropriate error handling
        - Consider performance implications
        - Ensure backward compatibility when appropriate

        # Communication
        Communicate clearly with the user:
        - Explain your reasoning and approach
        - Highlight important decisions or trade-offs
        - When showing code, be concise but complete
        - Use code snippets effectively to illustrate points
        - Ask clarifying questions when necessary

        # Boundaries
        Respect appropriate boundaries:
        - Focus on doing exactly what the user asks - no more, no less
        - If you think there is a clear follow-up task, ASK the user
        - Don't make assumptions about requirements without verification
        - Be transparent about limitations or potential issues

        # Testing
        Emphasize testing and verification:
        - Suggest testing strategies for code changes
        - Help write unit tests when appropriate
        - Explain how to verify that changes work as expected
        - Consider edge cases and potential failure modes

        # Problem Solving
        When facing difficulties:
        - Break down complex problems into smaller parts
        - Try alternative approaches when stuck
        - Look for similar patterns in the existing codebase
        - Ask the user for clarification or guidance when needed
        - Be persistent but know when to seek help
        """

    def _extract_tool_calls(self, response: Any) -> List[Dict[str, Any]]:
        """
        Extract tool calls from the LLM response

        Args:
            response: LLM response (string or dict with tool calls)

        Returns:
            List of tool calls with name and parameters
        """
        tool_calls = []

        # Check if the response is a dictionary with tool calls
        if isinstance(response, dict) and "tool_calls" in response:
            tool_calls = response["tool_calls"]
            logger.info(f"Extracted {len(tool_calls)} tool calls from LLM response")

            # Add the response content to conversation history
            if "content" in response and response["content"]:
                self.conversation_history.append({
                    "role": "assistant",
                    "content": response["content"]
                })
        else:
            # Try to parse tool calls from text response (for backward compatibility)
            import re
            if isinstance(response, str):
                # Look for tool call markers in the response
                # Example format: <tool>{"name": "tool_name", "parameters": {...}}</tool>
                tool_pattern = r'<tool>(.*?)</tool>'
                matches = re.findall(tool_pattern, response, re.DOTALL)

                for match in matches:
                    try:
                        tool_data = json.loads(match)
                        tool_calls.append({
                            "id": f"manual-{len(tool_calls)}",
                            "name": tool_data.get("name"),
                            "parameters": tool_data.get("parameters", {})
                        })
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse tool call: {match}")

        return tool_calls

    def _execute_tool_workflow(self, tool_calls: List[Dict[str, Any]],
                              original_request: str) -> str:
        """
        Execute a series of tool calls and generate a final response

        Args:
            tool_calls: List of tool calls to execute
            original_request: The original user request

        Returns:
            Final response after tool execution
        """
        tool_results = []
        max_iterations = 5  # Prevent infinite loops
        current_iteration = 0

        while tool_calls and current_iteration < max_iterations:
            current_iteration += 1
            logger.info(f"Tool execution iteration {current_iteration}/{max_iterations}")

            # Execute each tool call
            for tool_call in tool_calls:
                tool_id = tool_call.get("id", f"unknown-{len(tool_results)}")
                tool_name = tool_call.get("name")
                parameters = tool_call.get("parameters", {})

                logger.info(f"Executing tool: {tool_name} with parameters: {parameters}")

                if not self.tool_registry.has_tool(tool_name):
                    tool_results.append({
                        "tool_id": tool_id,
                        "tool_name": tool_name,
                        "status": "error",
                        "result": f"Tool '{tool_name}' not found"
                    })
                    continue

                try:
                    result = self.tool_registry.execute_tool(tool_name, **parameters)
                    tool_results.append({
                        "tool_id": tool_id,
                        "tool_name": tool_name,
                        "status": "success",
                        "result": result
                    })
                    logger.info(f"Tool {tool_name} executed successfully")
                except Exception as e:
                    error_msg = f"Error executing tool {tool_name}: {str(e)}"
                    logger.error(error_msg)
                    tool_results.append({
                        "tool_id": tool_id,
                        "tool_name": tool_name,
                        "status": "error",
                        "result": f"Error: {str(e)}"
                    })

            # Check if we need to continue with more tool calls
            if current_iteration < max_iterations:
                # Generate a new response based on the tool results so far
                response = self.llm_service.generate_response(
                    system_prompt=self._create_system_prompt(),
                    tools=self.tool_registry.get_tools_description(),
                    tool_results=tool_results,
                    original_request=original_request,
                    conversation_history=self.conversation_history
                )

                # Extract any new tool calls
                new_tool_calls = self._extract_tool_calls(response)

                if not new_tool_calls:
                    # No more tool calls, we're done
                    if isinstance(response, dict) and "content" in response:
                        return response["content"]
                    elif isinstance(response, str):
                        return response
                    break

                # Continue with the new tool calls
                tool_calls = new_tool_calls

        # Generate final response based on all tool results
        final_response = self._generate_final_response(original_request, tool_results)

        return final_response

    def _generate_final_response(self, original_request: str,
                                tool_results: List[Dict[str, Any]]) -> str:
        """
        Generate a final response based on tool execution results

        Args:
            original_request: The original user request
            tool_results: Results from tool executions

        Returns:
            Final response to the user
        """
        # Send the original request and tool results back to the LLM
        # to generate a coherent final response

        response = self.llm_service.generate_response(
            system_prompt=self._create_system_prompt(),
            tool_results=tool_results,
            original_request=original_request,
            conversation_history=self.conversation_history
        )

        return response

    def add_memory(self, memory: str) -> None:
        """
        Add a new memory to the memory system

        Args:
            memory: The memory text to store
        """
        self.memory_manager.add_memory(memory)
