"""
Memory Manager - Handles storage and retrieval of agent memories with semantic search
"""
import json
import os
import time
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
import logging
import re
from collections import defaultdict

# Import the embedding functionality
from .memory_embeddings import get_embedder

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MemoryManager:
    """
    Manages the agent's memory system for storing and retrieving information
    with semantic search capabilities similar to Augment
    """

    def __init__(self, memory_file: str = "agent_memories.json"):
        """
        Initialize the memory manager

        Args:
            memory_file: Path to the file where memories are stored
        """
        # Use absolute path for memory file to ensure consistency
        if not os.path.isabs(memory_file):
            # Get the directory of the current file (memory_manager.py)
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # Go up one level to the agent_framework directory
            agent_framework_dir = os.path.dirname(current_dir)
            # Create absolute path
            self.memory_file = os.path.join(agent_framework_dir, memory_file)
        else:
            self.memory_file = memory_file

        logger.info(f"Using memory file: {self.memory_file}")
        self.memories = self._load_memories()
        self.embedder = get_embedder()
        self.memory_embeddings = {}
        self.memory_clusters = defaultdict(list)
        self.importance_scores = {}

        # Generate embeddings for existing memories
        self._generate_embeddings()
        self._cluster_memories()

    def _load_memories(self) -> List[Dict[str, Any]]:
        """
        Load memories from the memory file

        Returns:
            List of memory entries
        """
        if not os.path.exists(self.memory_file):
            return []

        try:
            with open(self.memory_file, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError) as e:
            logger.error(f"Error loading memories: {str(e)}")
            return []

    def _save_memories(self) -> None:
        """Save memories to the memory file"""
        try:
            with open(self.memory_file, 'w') as f:
                json.dump(self.memories, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving memories: {str(e)}")

    def _generate_embeddings(self) -> None:
        """Generate embeddings for all memories"""
        for i, memory in enumerate(self.memories):
            memory_id = str(i)
            content = memory["content"]

            # Generate embedding for the memory content
            try:
                embedding = self.embedder.embed_text(content)
                self.memory_embeddings[memory_id] = embedding

                # Calculate importance score based on metadata
                importance = self._calculate_importance(memory)
                self.importance_scores[memory_id] = importance

                logger.debug(f"Generated embedding for memory {memory_id}")
            except Exception as e:
                logger.error(f"Error generating embedding for memory {memory_id}: {str(e)}")

    def _calculate_importance(self, memory: Dict[str, Any]) -> float:
        """
        Calculate importance score for a memory

        Args:
            memory: Memory entry

        Returns:
            Importance score between 0 and 1
        """
        # Start with base importance
        importance = 0.5

        # Adjust based on metadata
        metadata = memory.get("metadata", {})

        # Category-based importance
        category = metadata.get("category", "general")
        category_weights = {
            "identity": 0.9,    # Core identity memories are very important
            "preference": 0.8,  # User preferences are important
            "code": 0.7,        # Code-related memories are important
            "project": 0.7,     # Project-specific memories are important
            "general": 0.5      # General memories have average importance
        }
        importance = category_weights.get(category, 0.5)

        # Tag-based adjustments
        tags = metadata.get("tags", [])
        if "code" in tags:
            importance += 0.1
        if "preference" in tags:
            importance += 0.1

        # Recency adjustment (newer memories are slightly more important)
        timestamp = memory.get("timestamp", time.time())
        age_days = (time.time() - timestamp) / (60 * 60 * 24)
        recency_factor = max(0, 0.1 * (1 - min(age_days / 30, 1)))  # 0.1 boost for very recent memories
        importance += recency_factor

        # Cap importance between 0 and 1
        return max(0.1, min(importance, 1.0))

    def _cluster_memories(self) -> None:
        """Cluster memories by semantic similarity"""
        # Reset clusters
        self.memory_clusters = defaultdict(list)

        # Skip if we have very few memories
        if len(self.memories) < 5:
            return

        # Simple clustering based on categories and similarity
        for i, memory in enumerate(self.memories):
            memory_id = str(i)

            # Skip if we don't have an embedding
            if memory_id not in self.memory_embeddings:
                continue

            # Get category as primary cluster
            category = memory.get("metadata", {}).get("category", "general")
            self.memory_clusters[category].append(memory_id)

            # Find similar memories for secondary clustering
            embedding = self.memory_embeddings[memory_id]

            for j, other_memory in enumerate(self.memories):
                if i == j:
                    continue

                other_id = str(j)
                if other_id not in self.memory_embeddings:
                    continue

                other_embedding = self.memory_embeddings[other_id]
                similarity = self.embedder.similarity(embedding, other_embedding)

                # If very similar, add to a similarity-based cluster
                if similarity > 0.8:
                    cluster_name = f"similar_to_{memory_id}"
                    self.memory_clusters[cluster_name].append(other_id)

        logger.debug(f"Created {len(self.memory_clusters)} memory clusters")

    def add_memory(self, content: str, metadata: Optional[Dict[str, Any]] = None,
                  category: str = "general") -> None:
        """
        Add a new memory with categorization and embedding

        Args:
            content: The memory content
            metadata: Optional metadata for the memory
            category: Category of the memory (e.g., "code", "user_preference", "project", "general")
        """
        # Ensure content is not empty
        if not content or not content.strip():
            logger.warning("Attempted to add empty memory - ignoring")
            return

        # Create metadata if not provided
        if metadata is None:
            metadata = {}

        # Add category to metadata
        metadata["category"] = category

        # Add automatic tags based on content
        tags = self._generate_tags(content)
        if tags:
            metadata["tags"] = tags

        # Add semantic topics
        topics = self._extract_topics(content)
        if topics:
            metadata["topics"] = topics

        memory = {
            "content": content,
            "timestamp": time.time(),
            "metadata": metadata
        }

        # Check for semantic duplicates or very similar memories
        duplicate_found = False
        memory_embedding = self.embedder.embed_text(content)

        for i, existing_memory in enumerate(self.memories):
            # Check for exact duplicates
            if existing_memory["content"] == content:
                # Update timestamp and metadata instead of adding duplicate
                existing_memory["timestamp"] = memory["timestamp"]
                # Merge metadata, keeping existing values if not in new metadata
                for key, value in memory["metadata"].items():
                    existing_memory["metadata"][key] = value
                logger.info("Updated existing memory instead of adding duplicate")
                self._save_memories()
                return

            # Check for semantic similarity
            memory_id = str(i)
            if memory_id in self.memory_embeddings:
                existing_embedding = self.memory_embeddings[memory_id]
                similarity = self.embedder.similarity(memory_embedding, existing_embedding)

                # If very similar (threshold can be adjusted)
                if similarity > 0.9:
                    # Update the existing memory with new information
                    existing_memory["content"] = self._merge_similar_memories(
                        existing_memory["content"], content
                    )
                    existing_memory["timestamp"] = memory["timestamp"]

                    # Merge metadata
                    for key, value in memory["metadata"].items():
                        if key == "tags" and "tags" in existing_memory["metadata"]:
                            # Combine tags
                            existing_memory["metadata"]["tags"] = list(set(
                                existing_memory["metadata"]["tags"] + value
                            ))
                        else:
                            existing_memory["metadata"][key] = value

                    # Update embedding
                    self.memory_embeddings[memory_id] = self.embedder.embed_text(existing_memory["content"])

                    # Update importance score
                    self.importance_scores[memory_id] = self._calculate_importance(existing_memory)

                    logger.info(f"Merged with similar existing memory (similarity: {similarity:.2f})")
                    duplicate_found = True
                    self._save_memories()
                    break

        if not duplicate_found:
            # Add new memory
            memory_id = str(len(self.memories))
            self.memories.append(memory)

            # Generate embedding and importance score
            self.memory_embeddings[memory_id] = memory_embedding
            self.importance_scores[memory_id] = self._calculate_importance(memory)

            # Update clusters
            self._cluster_memories()

            self._save_memories()
            logger.info(f"Added new memory in category '{category}' with {len(tags)} tags")

    def _extract_topics(self, content: str) -> List[str]:
        """
        Extract main topics from content

        Args:
            content: Memory content

        Returns:
            List of extracted topics
        """
        # Simple topic extraction based on noun phrases and keywords
        topics = []

        # Lowercase the content
        content_lower = content.lower()

        # Check for programming languages
        programming_languages = ["python", "javascript", "typescript", "java", "c#", "c++", "ruby", "go", "rust"]
        for lang in programming_languages:
            if lang in content_lower:
                topics.append(lang)

        # Check for frameworks
        frameworks = ["react", "angular", "vue", "django", "flask", "express", "spring", "tensorflow", "pytorch"]
        for framework in frameworks:
            if framework in content_lower:
                topics.append(framework)

        # Check for concepts
        concepts = ["algorithm", "function", "class", "object", "variable", "database", "api",
                   "interface", "testing", "deployment", "security", "performance"]
        for concept in concepts:
            if concept in content_lower:
                topics.append(concept)

        # Return unique topics
        return list(set(topics))

    def _merge_similar_memories(self, existing_content: str, new_content: str) -> str:
        """
        Merge similar memory contents

        Args:
            existing_content: Content of existing memory
            new_content: Content of new memory

        Returns:
            Merged content
        """
        # If contents are identical, just return one of them
        if existing_content == new_content:
            return existing_content

        # If one is clearly a subset of the other, return the longer one
        if new_content in existing_content:
            return existing_content
        if existing_content in new_content:
            return new_content

        # Otherwise, combine them with a separator
        return f"{existing_content}\n\nAdditional information:\n{new_content}"

    def _generate_tags(self, content: str) -> List[str]:
        """
        Generate tags automatically from memory content

        Args:
            content: The memory content

        Returns:
            List of generated tags
        """
        tags = []

        # Check for code-related content
        code_indicators = ["def ", "class ", "function", "import ", "from ", "var ", "const ",
                          "let ", "public ", "private ", "interface ", "struct ", "<div", "<span"]
        for indicator in code_indicators:
            if indicator in content:
                tags.append("code")
                break

        # Check for file operations
        file_indicators = ["file", "directory", "folder", "path", "save", "read", "write", "open"]
        for indicator in file_indicators:
            if indicator.lower() in content.lower():
                tags.append("file_operation")
                break

        # Check for preferences
        preference_indicators = ["prefer", "want", "like", "don't like", "should", "always", "never"]
        for indicator in preference_indicators:
            if indicator.lower() in content.lower():
                tags.append("preference")
                break

        # Check for project-specific content
        project_indicators = ["project", "repository", "repo", "codebase", "system", "application", "app"]
        for indicator in project_indicators:
            if indicator.lower() in content.lower():
                tags.append("project")
                break

        return list(set(tags))  # Remove duplicates

    def get_all_memories(self) -> List[Dict[str, Any]]:
        """
        Get all stored memories

        Returns:
            List of all memory entries
        """
        return self.memories

    def get_memories_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        Get memories filtered by category

        Args:
            category: Category to filter by

        Returns:
            List of memory entries in the specified category
        """
        return [
            memory for memory in self.memories
            if memory.get("metadata", {}).get("category") == category
        ]

    def get_memories_by_tag(self, tag: str) -> List[Dict[str, Any]]:
        """
        Get memories filtered by tag

        Args:
            tag: Tag to filter by

        Returns:
            List of memory entries with the specified tag
        """
        return [
            memory for memory in self.memories
            if tag in memory.get("metadata", {}).get("tags", [])
        ]

    def search_memories(self, query: str, categories: Optional[List[str]] = None,
                       tags: Optional[List[str]] = None, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Advanced memory search with filtering

        Args:
            query: Search query
            categories: Optional list of categories to filter by
            tags: Optional list of tags to filter by
            max_results: Maximum number of results to return

        Returns:
            List of matching memory entries
        """
        # Start with all memories
        candidate_memories = self.memories

        # Filter by categories if specified
        if categories:
            candidate_memories = [
                memory for memory in candidate_memories
                if memory.get("metadata", {}).get("category") in categories
            ]

        # Filter by tags if specified
        if tags:
            filtered_memories = []
            for memory in candidate_memories:
                memory_tags = memory.get("metadata", {}).get("tags", [])
                if any(tag in memory_tags for tag in tags):
                    filtered_memories.append(memory)
            candidate_memories = filtered_memories

        # If no memories left after filtering, return empty list
        if not candidate_memories:
            return []

        # Score the remaining memories
        query_lower = query.lower()
        scored_memories = []

        for memory in candidate_memories:
            content = memory["content"].lower()
            score = 0

            # Score based on content match
            if query_lower in content:
                score += 5

            # Score based on word matches
            for word in query_lower.split():
                if len(word) > 2 and word in content:
                    score += 1

            # Add to results if score > 0
            if score > 0:
                scored_memories.append((score, memory))

        # Sort by score and return top results
        scored_memories.sort(reverse=True, key=lambda x: x[0])
        return [memory for _, memory in scored_memories[:max_results]]

    def get_relevant_memories(self, query: str, max_results: int = 5) -> List[str]:
        """
        Get memories relevant to a query using semantic search and advanced matching

        Args:
            query: The query to find relevant memories for
            max_results: Maximum number of memories to return

        Returns:
            List of relevant memory contents
        """
        if not self.memories:
            return []

        # Generate embedding for the query
        try:
            query_embedding = self.embedder.embed_text(query)
        except Exception as e:
            logger.error(f"Error generating query embedding: {str(e)}")
            query_embedding = None

        # Convert query to lowercase for text-based matching
        query_lower = query.lower()
        query_words = [w for w in query_lower.split() if len(w) > 2]  # Filter out short words

        # Extract topics from query for better matching
        query_topics = self._extract_topics(query)

        # Score memories using multiple relevance factors
        scored_memories = []

        for i, memory in enumerate(self.memories):
            memory_id = str(i)
            content = memory["content"].lower()
            metadata = memory.get("metadata", {})

            # Initialize score components
            semantic_score = 0
            text_match_score = 0
            recency_score = 0
            metadata_score = 0
            importance_score = 0

            # Component 1: Semantic similarity (highest weight)
            if query_embedding is not None and memory_id in self.memory_embeddings:
                memory_embedding = self.memory_embeddings[memory_id]
                similarity = self.embedder.similarity(query_embedding, memory_embedding)
                # Scale similarity from [-1,1] to [0,20] with higher weight
                semantic_score = (similarity + 1) * 10  # Max 20 points

            # Component 2: Text matching
            # Exact phrase matching
            if query_lower in content:
                text_match_score += 8

            # Word matching
            for word in query_words:
                if word in content:
                    # Count occurrences with diminishing returns
                    word_count = content.count(word)
                    text_match_score += min(word_count, 3) * 0.5  # Max 1.5 per word

            # Component 3: Recency
            timestamp = memory.get("timestamp", time.time())
            memory_age = time.time() - timestamp
            # Convert to days and cap at 30 days
            days_old = min(memory_age / (60 * 60 * 24), 30)
            recency_score = max(0, 1 - (days_old / 30)) * 3  # Max 3 points for recency

            # Component 4: Metadata matching
            # Category relevance
            category = metadata.get("category", "general")
            if "identity" in query_lower and category == "identity":
                metadata_score += 5
            elif "preference" in query_lower and category == "preference":
                metadata_score += 5

            # Topic matching
            memory_topics = metadata.get("topics", [])
            for topic in query_topics:
                if topic in memory_topics:
                    metadata_score += 2  # 2 points per matching topic

            # Tag matching
            memory_tags = metadata.get("tags", [])
            for word in query_words:
                if word in memory_tags:
                    metadata_score += 3  # 3 points per matching tag

            # Component 5: Importance score
            if memory_id in self.importance_scores:
                # Scale importance from [0,1] to [0,5]
                importance_score = self.importance_scores[memory_id] * 5

            # Calculate total score with weights
            total_score = (
                semantic_score * 1.0 +    # Semantic similarity (weight: 1.0)
                text_match_score * 0.7 +  # Text matching (weight: 0.7)
                recency_score * 0.5 +     # Recency (weight: 0.5)
                metadata_score * 0.8 +    # Metadata matching (weight: 0.8)
                importance_score * 0.6    # Importance (weight: 0.6)
            )

            # Add to scored memories if score is above threshold
            if total_score > 0:
                scored_memories.append({
                    "memory_id": memory_id,
                    "content": memory["content"],
                    "total_score": total_score,
                    "semantic_score": semantic_score,
                    "text_match_score": text_match_score,
                    "recency_score": recency_score,
                    "metadata_score": metadata_score,
                    "importance_score": importance_score,
                    "timestamp": timestamp
                })

        # Sort by total score (descending)
        scored_memories.sort(key=lambda x: x["total_score"], reverse=True)

        # Log detailed scoring for debugging
        for i, memory in enumerate(scored_memories[:max_results]):
            time_str = time.strftime('%Y-%m-%d', time.localtime(memory["timestamp"]))
            logger.debug(
                f"Memory {i+1}: Total Score {memory['total_score']:.2f} "
                f"(Semantic: {memory['semantic_score']:.2f}, "
                f"Text: {memory['text_match_score']:.2f}, "
                f"Recency: {memory['recency_score']:.2f}, "
                f"Metadata: {memory['metadata_score']:.2f}, "
                f"Importance: {memory['importance_score']:.2f}), "
                f"Date: {time_str}, Content: {memory['content'][:50]}..."
            )

        # Return just the memory contents
        return [memory["content"] for memory in scored_memories[:max_results]]

    def clear_memories(self) -> None:
        """Clear all memories"""
        self.memories = []
        self._save_memories()

    def delete_memory(self, index: int) -> bool:
        """
        Delete a memory by index

        Args:
            index: Index of the memory to delete

        Returns:
            True if successful, False otherwise
        """
        if 0 <= index < len(self.memories):
            del self.memories[index]
            self._save_memories()
            return True
        return False
