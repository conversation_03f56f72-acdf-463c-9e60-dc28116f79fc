"""
Position-based error mapping utilities for precise statement identification
"""

import re
from typing import List, Tuple, Optional, Dict, Any
from formatting.sql_splitter import split_sql_statements


class UniversalErrorExtractor:
    """Extract position information from any database error message"""

    def extract_position_info(self, error_message: str) -> Dict[str, int]:
        """Extract line or position information from error message"""
        position_info = {}

        # PostgreSQL line format: "LINE 139:"
        line_match = re.search(r'LINE\s+(\d+):', error_message, re.IGNORECASE)
        if line_match:
            position_info['line'] = int(line_match.group(1))
            return position_info

        # Position format: "Position: 17013" or "position 15420"
        pos_match = re.search(r'position[:\s]+(\d+)', error_message, re.IGNORECASE)
        if pos_match:
            position_info['position'] = int(pos_match.group(1))
            return position_info

        # Line format: "line: 447" or "line 184"
        line_match2 = re.search(r'line[:\s]+(\d+)', error_message, re.IGNORECASE)
        if line_match2:
            position_info['line'] = int(line_match2.group(1))
            return position_info

        return position_info

    def convert_line_to_position(self, line_number: int, original_sql: str) -> Optional[int]:
        """Convert line number to character position in original SQL"""
        try:
            lines = original_sql.split('\n')
            if line_number <= 0 or line_number > len(lines):
                return None

            # Calculate character position at start of the specified line
            position = sum(len(line) + 1 for line in lines[:line_number-1])  # +1 for newline
            return position
        except Exception:
            return None


class PositionTracker:
    """Track exact positions during format-preserving SQL splitting"""

    def __init__(self, sql_code: str):
        self.sql_code = sql_code

    def split_with_positions(self) -> List[Tuple[int, int]]:
        """Split SQL using format-preserving sql_splitter and track exact positions"""
        if not self.sql_code or not self.sql_code.strip():
            return []

        # Use format-preserving SQL splitter
        statements = split_sql_statements(self.sql_code, preserve_formatting=True)
        return self.calculate_exact_positions(statements)

    def calculate_exact_positions(self, statements: List[str]) -> List[Tuple[int, int]]:
        """Calculate exact positions for format-preserved statements"""
        positions = []
        search_start = 0

        for statement in statements:
            if not statement.strip():  # Skip empty statements
                continue

            # Find exact match (guaranteed with format preservation)
            stmt_pos = self.sql_code.find(statement, search_start)

            if stmt_pos != -1:
                positions.append((stmt_pos, stmt_pos + len(statement) - 1))
                search_start = stmt_pos + len(statement)
            else:
                # Should never happen with format preservation
                print(f"⚠️ Position tracking failed for: {statement[:30]}...")
                positions.append((search_start, search_start + len(statement) - 1))
                search_start += len(statement)

        return positions

    def find_statement_positions(self, _statements: List[str]) -> List[Tuple[int, int]]:
        """Legacy method - uses format-preserving approach"""
        return self.split_with_positions()




class AdvancedPositionMapper:
    """Advanced position mapping with comprehensive statement tracking"""
    
    def __init__(self):
        self.statement_ranges = []  # List of (start_pos, end_pos, start_line, end_line)
        self.position_to_statement = {}  # char_pos -> statement_number
        self.line_to_statement = {}  # line_number -> statement_number
        self.statement_content_hash = {}  # statement_number -> content_hash
        self.original_sql = ""
        self.lines = []
    
    def split_with_comprehensive_mapping(self, sql_code: str) -> Tuple[List[str], 'AdvancedPositionMapper']:
        """Split SQL using format-preserving sql_splitter and create comprehensive position mapping"""
        self.original_sql = sql_code
        self.lines = sql_code.split('\n')

        # Use the format-preserving SQL splitter to maintain exact positions
        statements = split_sql_statements(sql_code, preserve_formatting=True)

        # With format preservation, we can directly calculate positions
        tracked_positions = self.calculate_direct_positions(sql_code, statements)

        # Create position mapping using tracked positions
        self.create_position_mapping_from_positions(sql_code, statements, tracked_positions)

        print(f"🔧 Using format-preserving sql_splitter.py: {len(statements)} statements, exact positions maintained")

        return statements, self

    def calculate_direct_positions(self, sql_code: str, statements: List[str]) -> List[Tuple[int, int]]:
        """Calculate positions directly since formatting is preserved"""
        positions = []
        search_start = 0

        for statement in statements:
            if not statement.strip():  # Skip empty statements
                continue

            # Find the statement in the original SQL
            stmt_pos = sql_code.find(statement, search_start)

            if stmt_pos != -1:
                # Found exact match (should always work with format preservation)
                stmt_start = stmt_pos
                stmt_end = stmt_pos + len(statement) - 1
                positions.append((stmt_start, stmt_end))
                search_start = stmt_end + 1
            else:
                # This should rarely happen with format preservation
                print(f"⚠️ Could not find exact position for statement: {statement[:50]}...")
                # Use approximate position
                approximate_start = search_start
                approximate_end = search_start + len(statement) - 1
                positions.append((approximate_start, approximate_end))
                search_start = approximate_end + 1

        return positions

    def create_position_mapping_from_positions(self, sql_code: str, statements: List[str], tracked_positions: List[Tuple[int, int]]):
        """Create comprehensive position mapping from pre-tracked positions"""
        # Ensure we have matching statement counts
        if len(statements) != len(tracked_positions):
            print(f"⚠️ Position mapping failed: {len(statements)} statements vs {len(tracked_positions)} positions")
            return

        # Create precise mapping using exact tracked positions
        for stmt_num, (statement, (stmt_start, stmt_end)) in enumerate(zip(statements, tracked_positions), 1):
            # Calculate line numbers from original SQL
            lines_before = sql_code[:stmt_start].count('\n')
            lines_in_stmt = statement.count('\n')
            start_line = lines_before + 1
            end_line = start_line + lines_in_stmt

            # Store range with exact positions
            self.statement_ranges.append((stmt_start, stmt_end, start_line, end_line))

            # Map every character position to statement number
            for pos in range(stmt_start, stmt_end + 1):
                self.position_to_statement[pos] = stmt_num

            # Map every line to statement number
            for line in range(start_line, end_line + 1):
                self.line_to_statement[line] = stmt_num

            # Create content hash for duplicate detection (use original content)
            content_hash = hash(statement.strip().lower())
            self.statement_content_hash[stmt_num] = content_hash

        print(f"✅ Position mapping created: {len(self.position_to_statement)} positions, {len(self.line_to_statement)} lines")

    def create_position_mapping(self, sql_code: str, statements: List[str]):
        """Legacy method - kept for backward compatibility"""
        # Use the existing sql_splitter approach for consistency
        position_tracker = PositionTracker(sql_code)
        tracked_positions = position_tracker.find_statement_positions(statements)
        self.create_position_mapping_from_positions(sql_code, statements, tracked_positions)

    def has_valid_position_mapping(self) -> bool:
        """Check if position mapping was successfully created"""
        return len(self.position_to_statement) > 0 or len(self.line_to_statement) > 0

    def find_statement_by_any_position(self, position_info: Dict[str, int]) -> List[Tuple[int, str, int]]:
        """Find statement by line or character position with improved line-to-position conversion"""
        candidates = []

        # Handle line number by converting to character position first
        if 'line' in position_info:
            line_num = position_info['line']
            print(f"🔍 Looking for line {line_num} in {len(self.line_to_statement)} mapped lines")

            # Try direct line mapping first (for backward compatibility)
            if line_num in self.line_to_statement:
                stmt_num = self.line_to_statement[line_num]
                candidates.append((stmt_num, 'line', line_num))
                print(f"✅ Direct line mapping: Line {line_num} → Statement #{stmt_num}")
            else:
                print(f"⚠️ Line {line_num} not found in direct mapping")
                # Convert line to character position and try position mapping
                extractor = UniversalErrorExtractor()
                char_pos = extractor.convert_line_to_position(line_num, self.original_sql)
                if char_pos and char_pos in self.position_to_statement:
                    stmt_num = self.position_to_statement[char_pos]
                    candidates.append((stmt_num, 'line_converted', char_pos))
                    print(f"✅ Line-to-position conversion: Line {line_num} → Position {char_pos} → Statement #{stmt_num}")
                else:
                    print(f"❌ Line {line_num} could not be mapped to any statement")

        if 'position' in position_info:
            char_pos = position_info['position']
            if char_pos in self.position_to_statement:
                stmt_num = self.position_to_statement[char_pos]
                candidates.append((stmt_num, 'position', char_pos))
                print(f"✅ Direct position mapping: Position {char_pos} → Statement #{stmt_num}")
            else:
                print(f"❌ Position {char_pos} not found in mapping")

        return candidates

    def find_statement_by_fuzzy_position(self, position_info: Dict[str, int], tolerance: int = 50) -> List[Tuple[int, str, int]]:
        """Find statement using fuzzy position matching with tolerance"""
        candidates = []

        if 'position' in position_info:
            target_pos = position_info['position']

            # Look for positions within tolerance range
            for pos in range(max(0, target_pos - tolerance), target_pos + tolerance + 1):
                if pos in self.position_to_statement:
                    stmt_num = self.position_to_statement[pos]
                    distance = abs(pos - target_pos)
                    candidates.append((stmt_num, 'fuzzy_position', distance))

            # Sort by distance (closest first)
            candidates.sort(key=lambda x: x[2])

        if 'line' in position_info:
            target_line = position_info['line']

            # Look for lines within tolerance range
            for line in range(max(1, target_line - 5), target_line + 6):  # ±5 lines tolerance
                if line in self.line_to_statement:
                    stmt_num = self.line_to_statement[line]
                    distance = abs(line - target_line)
                    candidates.append((stmt_num, 'fuzzy_line', distance))

            # Sort by distance (closest first)
            candidates.sort(key=lambda x: x[2])

        return candidates

    def get_duplicate_statements(self, statement_num: int) -> List[int]:
        """Get all statements with same content hash (duplicates)"""
        if statement_num not in self.statement_content_hash:
            return [statement_num]
        
        target_hash = self.statement_content_hash[statement_num]
        duplicates = []
        
        for stmt_num, content_hash in self.statement_content_hash.items():
            if content_hash == target_hash:
                duplicates.append(stmt_num)
        
        return sorted(duplicates)
    
    def to_dict(self) -> Dict[str, Any]:
        """Serialize to dictionary with proper key handling"""
        return {
            'statement_ranges': self.statement_ranges,
            'position_to_statement': {str(k): v for k, v in self.position_to_statement.items()},
            'line_to_statement': {str(k): v for k, v in self.line_to_statement.items()},
            'statement_content_hash': {str(k): v for k, v in self.statement_content_hash.items()},
            'original_sql': self.original_sql,
            'lines': self.lines
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdvancedPositionMapper':
        """Deserialize from dictionary with proper key conversion"""
        if not data:
            raise ValueError("Cannot deserialize from empty data")

        mapper = cls()

        # Direct access without defaults - will raise KeyError if missing
        try:
            mapper.statement_ranges = data['statement_ranges']

            # Convert string keys back to integers
            mapper.position_to_statement = {int(k): v for k, v in data['position_to_statement'].items()}
            mapper.line_to_statement = {int(k): v for k, v in data['line_to_statement'].items()}
            mapper.statement_content_hash = {int(k): v for k, v in data['statement_content_hash'].items()}

            mapper.original_sql = data['original_sql']
            mapper.lines = data['lines']
        except KeyError as e:
            raise ValueError(f"Missing required key in position mapper data: {e}")
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid data format in position mapper: {e}")

        return mapper


class SmartStatementResolver:
    """Simple position-based statement resolver"""
    
    def __init__(self, position_mapper: AdvancedPositionMapper):
        self.position_mapper = position_mapper
    
    def resolve_statement_by_position(self, error_message: str, iteration_count: int = 1) -> Optional[int]:
        """Enhanced position-based statement resolution with syntax validation"""

        # Check if position mapping is valid before attempting resolution
        if not self.position_mapper.has_valid_position_mapping():
            print("⚠️ Position mapping not valid, falling back to AI")
            return None

        # Extract position information
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)

        if not position_info:
            print("⚠️ No position information found in error message")
            return None

        print(f"🎯 Extracted position info: {position_info}")

        # Strategy 1: Exact position matching with syntax validation
        candidates = self.position_mapper.find_statement_by_any_position(position_info)

        if candidates:
            print(f"✅ Found {len(candidates)} exact position matches")
            # Validate that the candidate actually contains relevant syntax
            validated_candidate = self._validate_candidate_syntax(candidates, error_message, iteration_count)
            if validated_candidate:
                return validated_candidate
            else:
                print("⚠️ Position-based candidate failed syntax validation")

        # Strategy 2: Fuzzy position matching with syntax validation
        print("🔍 Trying fuzzy position matching...")
        fuzzy_candidates = self.position_mapper.find_statement_by_fuzzy_position(position_info)

        if fuzzy_candidates:
            print(f"✅ Found {len(fuzzy_candidates)} fuzzy position matches")
            validated_candidate = self._validate_candidate_syntax(fuzzy_candidates, error_message, iteration_count)
            if validated_candidate:
                return validated_candidate
            else:
                print("⚠️ Fuzzy position candidate failed syntax validation")

        print("❌ No valid position matches found, falling back to AI")
        return None

    def _validate_candidate_syntax(self, candidates: List[Tuple[int, str, int]], error_message: str, iteration_count: int) -> Optional[int]:
        """Validate that the candidate statement actually contains syntax relevant to the error"""
        if not candidates:
            return None

        # Get the statement content for validation
        candidate_stmt_num = candidates[0][0]  # Take first candidate

        # Get statement ranges to extract content
        if candidate_stmt_num <= len(self.position_mapper.statement_ranges):
            stmt_start, stmt_end, _, _ = self.position_mapper.statement_ranges[candidate_stmt_num - 1]
            statement_content = self.position_mapper.original_sql[stmt_start:stmt_end + 1]

            print(f"🔍 Validating candidate statement #{candidate_stmt_num}")
            print(f"📝 Statement content: {statement_content[:100]}...")

            # Basic syntax validation based on error type
            if "mismatched parentheses" in error_message.lower():
                if "(" in statement_content or ")" in statement_content:
                    print("✅ Statement contains parentheses - syntax validation passed")
                    return self._select_best_candidate(candidates, iteration_count)
                else:
                    print("❌ Statement has no parentheses but error is about mismatched parentheses")
                    return None

            elif "syntax error" in error_message.lower():
                # For general syntax errors, accept the position-based result
                print("✅ General syntax error - accepting position-based result")
                return self._select_best_candidate(candidates, iteration_count)

            else:
                # For other errors, accept the position-based result
                print("✅ Non-syntax error - accepting position-based result")
                return self._select_best_candidate(candidates, iteration_count)

        print("❌ Could not extract statement content for validation")
        return None

    def _select_best_candidate(self, candidates: List[Tuple[int, str, int]], iteration_count: int) -> int:
        """Select the best candidate from position matches"""
        if len(candidates) == 1:
            statement_num = candidates[0][0]
            print(f"🎯 Single candidate: Statement #{statement_num}")
            return statement_num

        # Multiple candidates - use iteration to select different ones
        first_candidate = candidates[0][0]
        duplicates = self.position_mapper.get_duplicate_statements(first_candidate)

        if len(duplicates) > 1:
            # Rotate through duplicates based on iteration
            selected_index = (iteration_count - 1) % len(duplicates)
            selected_statement = duplicates[selected_index]
            print(f"🔄 Multiple duplicates: Selected statement #{selected_statement} (iteration {iteration_count})")
            return selected_statement

        # No duplicates, just return first candidate
        statement_num = candidates[0][0]
        print(f"🎯 Best candidate: Statement #{statement_num}")
        return statement_num
