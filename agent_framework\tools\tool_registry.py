"""
Tool Registry - Manages all available tools for the agent
"""
import inspect
from typing import Dict, Any, Callable, List, Optional

class ToolRegistry:
    """
    Registry for all tools available to the agent
    """
    
    def __init__(self):
        """Initialize an empty tool registry"""
        self.tools: Dict[str, Callable] = {}
        self.tool_descriptions: Dict[str, Dict[str, Any]] = {}
        
    def register_tool(self, func: Callable, name: Optional[str] = None, 
                     description: str = "", parameter_descriptions: Dict[str, str] = None) -> None:
        """
        Register a new tool in the registry
        
        Args:
            func: The function implementing the tool
            name: Optional custom name for the tool (defaults to function name)
            description: Description of what the tool does
            parameter_descriptions: Descriptions for each parameter
        """
        tool_name = name or func.__name__
        
        # Get function signature for parameters
        signature = inspect.signature(func)
        parameters = {}
        
        for param_name, param in signature.parameters.items():
            # Skip self parameter for methods
            if param_name == 'self':
                continue
                
            param_type = "string"  # Default type
            if param.annotation != inspect.Parameter.empty:
                if param.annotation == str:
                    param_type = "string"
                elif param.annotation == int:
                    param_type = "integer"
                elif param.annotation == float:
                    param_type = "number"
                elif param.annotation == bool:
                    param_type = "boolean"
                elif param.annotation == dict or param.annotation == Dict:
                    param_type = "object"
                elif param.annotation == list or param.annotation == List:
                    param_type = "array"
            
            # Check if parameter is required
            required = param.default == inspect.Parameter.empty
            
            # Get description from parameter_descriptions if available
            param_description = ""
            if parameter_descriptions and param_name in parameter_descriptions:
                param_description = parameter_descriptions[param_name]
            
            parameters[param_name] = {
                "type": param_type,
                "description": param_description,
                "required": required
            }
        
        # Store the tool and its metadata
        self.tools[tool_name] = func
        self.tool_descriptions[tool_name] = {
            "name": tool_name,
            "description": description or func.__doc__ or "",
            "parameters": parameters
        }
    
    def has_tool(self, name: str) -> bool:
        """
        Check if a tool exists in the registry
        
        Args:
            name: Name of the tool to check
            
        Returns:
            True if the tool exists, False otherwise
        """
        return name in self.tools
    
    def execute_tool(self, name: str, **kwargs) -> Any:
        """
        Execute a tool with the given parameters
        
        Args:
            name: Name of the tool to execute
            **kwargs: Parameters to pass to the tool
            
        Returns:
            Result of the tool execution
            
        Raises:
            ValueError: If the tool doesn't exist
        """
        if not self.has_tool(name):
            raise ValueError(f"Tool '{name}' not found in registry")
        
        tool_func = self.tools[name]
        return tool_func(**kwargs)
    
    def get_tools_description(self) -> List[Dict[str, Any]]:
        """
        Get descriptions of all registered tools
        
        Returns:
            List of tool descriptions in a format suitable for LLM
        """
        return list(self.tool_descriptions.values())
    
    def get_tool_description(self, name: str) -> Dict[str, Any]:
        """
        Get description of a specific tool
        
        Args:
            name: Name of the tool
            
        Returns:
            Tool description
            
        Raises:
            ValueError: If the tool doesn't exist
        """
        if not self.has_tool(name):
            raise ValueError(f"Tool '{name}' not found in registry")
        
        return self.tool_descriptions[name]
