"""
Enhanced prompts for handling edge cases in database conversion workflow.
Specifically designed to handle scenarios with minimal context (1-2 statements).
"""

def create_edge_case_validation_prompt(error_context, error_message: str, context_type: str = "minimal") -> str:
    """
    Create enhanced validation prompt for edge cases with minimal context.
    
    Args:
        error_context: ErrorContext object with potentially minimal statements
        error_message: Database deployment error message
        context_type: Type of edge case ("minimal", "single", "first_statement", "last_statement")
    """
    
    # Count available context
    available_statements = []
    if error_context.before_statement_number > 0:
        available_statements.append(f"Before: #{error_context.before_statement_number}")
    if error_context.error_statement_number > 0:
        available_statements.append(f"Error: #{error_context.error_statement_number}")
    if error_context.after_statement_number > 0:
        available_statements.append(f"After: #{error_context.after_statement_number}")
    
    context_summary = ", ".join(available_statements)
    
    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems.

EDGE CASE SCENARIO DETECTED:
- Context Type: {context_type.upper()}
- Available Statements: {context_summary}
- Total Context: {len(available_statements)} statement(s)

This is an EDGE CASE with minimal context. Apply specialized validation logic for limited context scenarios.

ERROR MESSAGE:
{error_message}

AVAILABLE CONTEXT STATEMENTS:
{_format_context_statements(error_context)}

EDGE CASE VALIDATION STRATEGY:
When context is minimal (1-2 statements), focus on:

1. DIRECT ERROR CORRELATION:
   - Does the error statement directly match the error message patterns?
   - Are there specific syntax elements that clearly cause the reported error?
   - Can you identify Oracle-specific constructs that don't work in PostgreSQL?

2. ERROR MESSAGE ANALYSIS:
   - Parse error message for specific line numbers, character positions
   - Look for exact syntax patterns mentioned in the error
   - Identify specific PostgreSQL error types (syntax, constraint, type mismatch)

3. STATEMENT CONTENT ANALYSIS:
   - Examine the error statement for obvious PostgreSQL incompatibilities
   - Look for Oracle-specific functions, syntax, or constructs
   - Check for data type mismatches or constraint violations

4. EDGE CASE CONSIDERATIONS:
   - With minimal context, rely more heavily on direct error-statement correlation
   - Accept validation if error statement clearly matches error message
   - Don't penalize for lack of surrounding context in edge cases
   - Focus on what IS available rather than what's missing

VALIDATION DECISION:
Based on the available context and error message, determine if the identified error statement is correct.

For edge cases with minimal context:
- ACCEPT if error statement clearly correlates with error message
- ACCEPT if obvious Oracle-PostgreSQL syntax issues are present
- ACCEPT if error patterns directly match statement content
- REJECT only if there's clear evidence the wrong statement was identified

OUTPUT FORMAT (JSON):
{{
  "is_correct": <boolean>,
  "confidence": <float between 0.0 and 1.0>,
  "explanation": "<detailed explanation including: 1) Edge case handling applied, 2) Direct error correlation analysis, 3) Statement content analysis, 4) Validation decision reasoning, 5) Confidence assessment for minimal context scenario>"
}}

IMPORTANT NOTES:
- This is an edge case with minimal context - adjust validation criteria accordingly
- Focus on direct error-statement correlation rather than context relationships
- Provide detailed explanation of edge case handling applied
- Be more lenient with validation when context is genuinely limited"""

def _format_context_statements(error_context) -> str:
    """Format context statements for display in prompts."""
    statements = []
    
    if error_context.before_statement_number > 0:
        statements.append(f"BEFORE STATEMENT #{error_context.before_statement_number}:")
        statements.append(f"{error_context.before_statement}")
        statements.append("")
    
    if error_context.error_statement_number > 0:
        statements.append(f"ERROR STATEMENT #{error_context.error_statement_number}:")
        statements.append(f"{error_context.error_statement}")
        statements.append("")
    
    if error_context.after_statement_number > 0:
        statements.append(f"AFTER STATEMENT #{error_context.after_statement_number}:")
        statements.append(f"{error_context.after_statement}")
        statements.append("")
    
    return "\n".join(statements)

def create_edge_case_source_mapping_prompt(source_statements, target_context, context_type: str = "minimal") -> str:
    """
    Create enhanced source mapping prompt for edge cases with minimal target context.
    """
    
    target_context_summary = f"{len(target_context)} target statement(s)"
    
    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems.

EDGE CASE SCENARIO DETECTED:
- Context Type: {context_type.upper()}
- Available Target Context: {target_context_summary}

This is an EDGE CASE with minimal target context. Apply specialized mapping logic for limited context scenarios.

SOURCE STATEMENTS:
{_format_numbered_statements(source_statements)}

TARGET CONTEXT (LIMITED):
{_format_target_context(target_context)}

EDGE CASE MAPPING STRATEGY:
When target context is minimal (1-2 statements), focus on:

1. BUSINESS OUTCOME EQUIVALENCE:
   - What business outcome does the target statement achieve?
   - Find source statements that accomplish the same business goal
   - Look for functional equivalence rather than syntax similarity

2. OPERATION TYPE MATCHING:
   - Identify the type of operation (SELECT, INSERT, UPDATE, DELETE, etc.)
   - Find source statements performing the same operation type
   - Match based on operation purpose and data manipulation

3. EDGE CASE CONSIDERATIONS:
   - With minimal target context, rely more on individual statement analysis
   - Focus on what the statement DOES rather than its position in sequence
   - Accept reasonable mappings even without perfect sequential context
   - Prioritize functional equivalence over positional relationships

4. ORACLE-POSTGRESQL PATTERN RECOGNITION:
   - Identify Oracle constructs that were converted to PostgreSQL equivalents
   - Look for common translation patterns between the databases
   - Recognize equivalent implementations with different syntax

MAPPING DECISION:
Find the source statement that achieves the same business outcome as the target error statement.

For edge cases with minimal context:
- Focus on functional equivalence of individual statements
- Accept mappings based on business outcome similarity
- Don't require perfect sequential context when it's not available
- Prioritize what statements accomplish over their relationships

OUTPUT FORMAT (JSON):
{{
  "source_statement_number": <integer>,
  "confidence_score": <float between 0.0 and 1.0>,
  "reasoning": "<detailed explanation including: 1) Edge case handling applied, 2) Business outcome analysis, 3) Functional equivalence assessment, 4) Mapping decision reasoning, 5) Confidence assessment for minimal context scenario>"
}}

IMPORTANT NOTES:
- This is an edge case with minimal target context - adjust mapping criteria accordingly
- Focus on business outcome equivalence rather than sequential relationships
- Provide detailed explanation of edge case handling applied
- Be more flexible with mapping when context is genuinely limited"""

def _format_numbered_statements(statements) -> str:
    """Format numbered statements for display."""
    return "\n".join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(statements)])

def _format_target_context(target_context) -> str:
    """Format target context for display."""
    formatted = []
    for stmt_num, stmt_text in target_context:
        formatted.append(f"Target Statement #{stmt_num}:")
        formatted.append(f"{stmt_text}")
        formatted.append("")
    return "\n".join(formatted)
