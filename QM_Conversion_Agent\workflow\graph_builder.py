import os
import uuid
from typing import Dict, Any
from langgraph.graph import StateGraph, END, START
from langgraph.checkpoint.memory import MemorySaver
from state import WorkflowState
from nodes.conversion_nodes import UniversalCodeMigrationNodes
from langchain_core.runnables.graph import MermaidDrawMethod



class GraphBuilder:
    def __init__(self, llm):
        self.llm = llm
        self.builder = StateGraph(WorkflowState)
        self.memory = MemorySaver()

    def build_graph(self):
        """
            Configure the graph by adding nodes, edges
        """
        # Create a new builder to avoid any cached nodes
        self.builder = StateGraph(WorkflowState)
        self.conversion_nodes = UniversalCodeMigrationNodes(llm=self.llm)

        # Add nodes for iterative validation and correction workflow
        self.builder.add_node("splitStatements", self.conversion_nodes.splitStatements)
        self.builder.add_node("mapStatements", self.conversion_nodes.mapStatements)
        self.builder.add_node("validateMappings", self.conversion_nodes.validateMappings)
        self.builder.add_node("correctMappings", self.conversion_nodes.correctMappings)
        self.builder.add_node("removeDuplicates", self.conversion_nodes.removeDuplicates)
        self.builder.add_node("sortMappings", self.conversion_nodes.sortMappings)

        # Define workflow: split → map → validate → correct → validate (loop) → end
        self.builder.add_edge(START, "splitStatements")
        self.builder.add_edge("splitStatements", "mapStatements")
        self.builder.add_edge("mapStatements", "validateMappings")

        # Add conditional edges for validation loop
        self.builder.add_conditional_edges(
            "validateMappings",
            self.should_correct_mappings,
            {
                "correct": "correctMappings",
                "removeDuplicates": "removeDuplicates",
                "end": END
            }
        )

        # After correction, validate again
        self.builder.add_edge("correctMappings", "validateMappings")

        # After duplicate removal, sort mappings
        self.builder.add_edge("removeDuplicates", "sortMappings")

        # After sorting, end workflow
        self.builder.add_edge("sortMappings", END)


        return self.builder

    def should_correct_mappings(self, state: WorkflowState) -> str:
        """Determine if mappings need correction based on validation results."""
        validation_passed = getattr(state, 'validation_passed', None)
        wrong_mappings = getattr(state, 'wrong_mappings', [])
        correction_attempts = getattr(state, 'correction_attempts', 0)

        # Limit correction attempts to prevent infinite loops
        max_attempts = 10

        print(f"🔍 Validation Decision: passed={validation_passed}, wrong_mappings={len(wrong_mappings)}, attempts={correction_attempts}")

        if validation_passed is True and len(wrong_mappings) == 0:
            print("✅ Validation PASSED - proceeding to duplicate removal")
            return "removeDuplicates"
        elif correction_attempts >= max_attempts:
            print(f"⚠️ Maximum correction attempts ({max_attempts}) reached - ending workflow")
            return "end"
        else:
            print(f"❌ Validation FAILED - {len(wrong_mappings)} wrong mappings found, proceeding to correction (attempt {correction_attempts + 1}/{max_attempts})")
            return "correct"

    def setup_graph(self):
        builder = self.build_graph()
        self.graph = builder.compile(
            interrupt_before=[], checkpointer=self.memory
        )
        return self.graph

    def invoke_graph(self, data: Dict[str, Any], thread_id: str = None) -> Dict[str, Any]:
        """
        Invoke the graph with the given input data.

        Args:
            input_data: Dictionary containing the input data for the workflow
            thread_id: Optional thread ID for the workflow execution

        Returns:
            Dictionary containing the workflow result
        """
        thread_id = thread_id or f"thread_{uuid.uuid4()}"
        thread = {"configurable": {"thread_id": thread_id}}
        return self.graph.invoke(data, config=thread)

    def save_graph_image(self, graph):
        try:
            # Generate the PNG image
            img_data = graph.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API
                )

            # # Generate the PNG image using Pyppeteer (local browser rendering)
            # img_data = graph.get_graph().draw_mermaid_png(
            #     draw_method=MermaidDrawMethod.PYPPETEER,
            #     max_retries=5,
            #     retry_delay=2.0
            # )

            # Save the image to a file
            graph_path = "workflow_graph.png"
            with open(graph_path, "wb") as f:
                f.write(img_data)

            print(f"Graph image saved to {graph_path}")
        except Exception as e:
            print(f"Warning: Could not generate graph image: {str(e)}")
            print("Continuing execution without graph visualization...")