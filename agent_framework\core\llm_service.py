"""
LLM Service - Handles interactions with the language model
"""
import json
import logging
import os
from typing import Dict, List, Any, Optional, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LLMService:
    """
    Service for interacting with Language Models (LLMs)
    """

    def __init__(self, provider: str = "openai", api_key: Optional[str] = None,
                model: str = "gpt-4", azure_endpoint: Optional[str] = None,
                azure_deployment: Optional[str] = None, api_version: str = "2023-05-15",
                temperature: float = 0.7, max_tokens: int = 4096):
        """
        Initialize the LLM service

        Args:
            provider: LLM provider (e.g., "openai", "azure-openai", "anthropic")
            api_key: API key for the provider (defaults to environment variable)
            model: Model to use
            azure_endpoint: Azure OpenAI endpoint URL (required for azure-openai)
            azure_deployment: Azure OpenAI deployment name (required for azure-openai)
            api_version: Azure OpenAI API version (default: 2023-05-15)
            temperature: Temperature parameter for the model (default: 0.7)
            max_tokens: Maximum number of tokens to generate (default: 4096)
        """
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.provider = provider.lower()
        self.model = model

        # Get API key from environment variable if not provided
        if api_key is None:
            if self.provider in ["openai", "azure-openai"]:
                api_key = os.environ.get("OPENAI_API_KEY")
            elif self.provider == "anthropic":
                api_key = os.environ.get("ANTHROPIC_API_KEY")

        self.api_key = api_key

        # Initialize the appropriate client based on provider
        if self.provider == "openai":
            try:
                import openai
                self.client = openai.OpenAI(api_key=self.api_key)
            except ImportError:
                logger.error("OpenAI package not installed. Install with: pip install openai")
                raise
        elif self.provider == "azure-openai":
            try:
                import openai

                # Get Azure endpoint from environment variable if not provided
                if azure_endpoint is None:
                    azure_endpoint = os.environ.get("AZURE_OPENAI_ENDPOINT")

                if azure_endpoint is None:
                    raise ValueError("Azure OpenAI endpoint is required. Provide it as a parameter or set AZURE_OPENAI_ENDPOINT environment variable.")

                # Get Azure deployment from environment variable if not provided
                if azure_deployment is None:
                    azure_deployment = os.environ.get("AZURE_OPENAI_DEPLOYMENT")

                if azure_deployment is None:
                    # If no deployment is specified, use the model name as the deployment name
                    azure_deployment = self.model

                self.azure_endpoint = azure_endpoint
                self.azure_deployment = azure_deployment
                self.api_version = api_version

                # Initialize Azure OpenAI client
                self.client = openai.AzureOpenAI(
                    api_key=self.api_key,
                    api_version=self.api_version,
                    azure_endpoint=self.azure_endpoint
                )

                # Store the deployment name to use instead of model name
                self.deployment_name = self.azure_deployment

            except ImportError:
                logger.error("OpenAI package not installed. Install with: pip install openai")
                raise
        elif self.provider == "anthropic":
            try:
                import anthropic
                self.client = anthropic.Anthropic(api_key=self.api_key)
            except ImportError:
                logger.error("Anthropic package not installed. Install with: pip install anthropic")
                raise
        else:
            raise ValueError(f"Unsupported provider: {provider}")

    def generate_response(self, system_prompt: str, user_input: Optional[str] = None,
                         tools: Optional[List[Dict[str, Any]]] = None,
                         memories: Optional[List[str]] = None,
                         conversation_history: Optional[List[Dict[str, str]]] = None,
                         tool_results: Optional[List[Dict[str, Any]]] = None,
                         original_request: Optional[str] = None) -> Union[str, Dict[str, Any]]:
        """
        Generate a response from the LLM

        Args:
            system_prompt: System prompt defining behavior
            user_input: Current user input (if any)
            tools: Available tools description (if any)
            memories: Relevant memories (if any)
            conversation_history: Previous conversation (if any)
            tool_results: Results from tool executions (if any)
            original_request: Original user request (for final responses)

        Returns:
            Generated response from the LLM (string or dict with tool calls)
        """
        if self.provider == "openai":
            return self._generate_openai_response(
                system_prompt, user_input, tools, memories,
                conversation_history, tool_results, original_request
            )
        elif self.provider == "azure-openai":
            return self._generate_azure_openai_response(
                system_prompt, user_input, tools, memories,
                conversation_history, tool_results, original_request
            )
        elif self.provider == "anthropic":
            return self._generate_anthropic_response(
                system_prompt, user_input, tools, memories,
                conversation_history, tool_results, original_request
            )
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")

    def _generate_openai_response(self, system_prompt: str, user_input: Optional[str] = None,
                                tools: Optional[List[Dict[str, Any]]] = None,
                                memories: Optional[List[str]] = None,
                                conversation_history: Optional[List[Dict[str, str]]] = None,
                                tool_results: Optional[List[Dict[str, Any]]] = None,
                                original_request: Optional[str] = None) -> Union[str, Dict[str, Any]]:
        """Generate response using OpenAI API"""
        try:
            # Prepare messages
            messages = []

            # Add system message
            full_system_prompt = system_prompt

            # Add memories if available
            if memories and len(memories) > 0:
                memories_str = "\n".join([f"- {memory}" for memory in memories])
                full_system_prompt += f"\n\nRelevant memories:\n{memories_str}"

            messages.append({"role": "system", "content": full_system_prompt})

            # Add conversation history
            if conversation_history:
                for message in conversation_history:
                    messages.append(message)

            # Add tool results if available
            if tool_results:
                tool_results_str = json.dumps(tool_results, indent=2)
                messages.append({
                    "role": "assistant",
                    "content": None,
                    "tool_calls": [{"id": "previous_tool_call", "type": "function", "function": {"name": "previous_tools", "arguments": "{}"}}]
                })
                messages.append({
                    "role": "tool",
                    "tool_call_id": "previous_tool_call",
                    "content": f"Results from tool executions:\n{tool_results_str}"
                })

                if original_request:
                    messages.append({
                        "role": "user",
                        "content": f"Based on these results, please provide a final response to my original request: {original_request}"
                    })

            # Add current user input if available
            if user_input and not any(msg.get("content") == user_input and msg.get("role") == "user"
                                    for msg in messages):
                messages.append({"role": "user", "content": user_input})

            # Format tools for OpenAI
            formatted_tools = None
            if tools:
                formatted_tools = []
                for tool in tools:
                    formatted_tool = {
                        "type": "function",
                        "function": {
                            "name": tool["name"],
                            "description": tool["description"],
                            "parameters": {
                                "type": "object",
                                "properties": {},
                                "required": []
                            }
                        }
                    }

                    # Add parameters
                    if "parameters" in tool:
                        for param_name, param_info in tool["parameters"].items():
                            formatted_tool["function"]["parameters"]["properties"][param_name] = {
                                "type": param_info.get("type", "string"),
                                "description": param_info.get("description", "")
                            }

                            if param_info.get("required", False):
                                formatted_tool["function"]["parameters"]["required"].append(param_name)

                    formatted_tools.append(formatted_tool)

            # Call the API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                tools=formatted_tools,
                tool_choice="auto"
            )

            message = response.choices[0].message

            # Check if the model wants to call a tool
            if message.tool_calls:
                # Return the tool calls for processing
                tool_calls = []
                for tool_call in message.tool_calls:
                    if tool_call.type == "function":
                        function_call = tool_call.function
                        tool_calls.append({
                            "id": tool_call.id,
                            "name": function_call.name,
                            "parameters": json.loads(function_call.arguments)
                        })

                return {
                    "content": message.content,
                    "tool_calls": tool_calls
                }

            # Otherwise return just the content
            return message.content

        except Exception as e:
            logger.error(f"Error generating OpenAI response: {str(e)}")
            return f"Error generating response: {str(e)}"

    def _generate_azure_openai_response(self, system_prompt: str, user_input: Optional[str] = None,
                                    tools: Optional[List[Dict[str, Any]]] = None,
                                    memories: Optional[List[str]] = None,
                                    conversation_history: Optional[List[Dict[str, str]]] = None,
                                    tool_results: Optional[List[Dict[str, Any]]] = None,
                                    original_request: Optional[str] = None) -> Union[str, Dict[str, Any]]:
        """Generate response using Azure OpenAI API"""
        try:
            # Prepare messages
            messages = []

            # Add system message
            full_system_prompt = system_prompt

            # Add memories if available
            if memories and len(memories) > 0:
                memories_str = "\n".join([f"- {memory}" for memory in memories])
                full_system_prompt += f"\n\nRelevant memories:\n{memories_str}"

            messages.append({"role": "system", "content": full_system_prompt})

            # Add conversation history
            if conversation_history:
                for message in conversation_history:
                    messages.append(message)

            # Add tool results if available
            if tool_results:
                tool_results_str = json.dumps(tool_results, indent=2)
                messages.append({
                    "role": "assistant",
                    "content": None,
                    "tool_calls": [{"id": "previous_tool_call", "type": "function", "function": {"name": "previous_tools", "arguments": "{}"}}]
                })
                messages.append({
                    "role": "tool",
                    "tool_call_id": "previous_tool_call",
                    "content": f"Results from tool executions:\n{tool_results_str}"
                })

                if original_request:
                    messages.append({
                        "role": "user",
                        "content": f"Based on these results, please provide a final response to my original request: {original_request}"
                    })

            # Add current user input if available
            if user_input and not any(msg.get("content") == user_input and msg.get("role") == "user"
                                    for msg in messages):
                messages.append({"role": "user", "content": user_input})

            # Format tools for Azure OpenAI
            formatted_tools = None
            if tools:
                formatted_tools = []
                for tool in tools:
                    formatted_tool = {
                        "type": "function",
                        "function": {
                            "name": tool["name"],
                            "description": tool["description"],
                            "parameters": {
                                "type": "object",
                                "properties": {},
                                "required": []
                            }
                        }
                    }

                    # Add parameters
                    if "parameters" in tool:
                        for param_name, param_info in tool["parameters"].items():
                            formatted_tool["function"]["parameters"]["properties"][param_name] = {
                                "type": param_info.get("type", "string"),
                                "description": param_info.get("description", "")
                            }

                            if param_info.get("required", False):
                                formatted_tool["function"]["parameters"]["required"].append(param_name)

                    formatted_tools.append(formatted_tool)

            # Call the Azure OpenAI API
            response = self.client.chat.completions.create(
                model=self.deployment_name,  # Use deployment name as the model name for Azure
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                tools=formatted_tools,
                tool_choice="auto"
            )

            message = response.choices[0].message

            # Check if the model wants to call a tool
            if message.tool_calls:
                # Return the tool calls for processing
                tool_calls = []
                for tool_call in message.tool_calls:
                    if tool_call.type == "function":
                        function_call = tool_call.function
                        tool_calls.append({
                            "id": tool_call.id,
                            "name": function_call.name,
                            "parameters": json.loads(function_call.arguments)
                        })

                return {
                    "content": message.content,
                    "tool_calls": tool_calls
                }

            # Otherwise return just the content
            return message.content

        except Exception as e:
            logger.error(f"Error generating Azure OpenAI response: {str(e)}")
            return f"Error generating response: {str(e)}"

    def _generate_anthropic_response(self, system_prompt: str, user_input: Optional[str] = None,
                                   tools: Optional[List[Dict[str, Any]]] = None,
                                   memories: Optional[List[str]] = None,
                                   conversation_history: Optional[List[Dict[str, str]]] = None,
                                   tool_results: Optional[List[Dict[str, Any]]] = None,
                                   original_request: Optional[str] = None) -> Union[str, Dict[str, Any]]:
        """Generate response using Anthropic API"""
        try:
            # Prepare messages for Anthropic format
            messages = []

            # Add system message
            full_system_prompt = system_prompt

            # Add memories if available
            if memories and len(memories) > 0:
                memories_str = "\n".join([f"- {memory}" for memory in memories])
                full_system_prompt += f"\n\nRelevant memories:\n{memories_str}"

            # Add conversation history
            if conversation_history:
                for message in conversation_history:
                    role = "user" if message["role"] == "user" else "assistant"
                    messages.append({"role": role, "content": message["content"]})

            # Add tool results if available
            if tool_results:
                tool_results_str = json.dumps(tool_results, indent=2)

                # Add as assistant message with tool use
                messages.append({
                    "role": "assistant",
                    "content": f"I used tools to help answer your question. Here are the results:\n\n{tool_results_str}"
                })

                if original_request:
                    messages.append({
                        "role": "user",
                        "content": f"Based on these results, please provide a final response to my original request: {original_request}"
                    })

            # Add current user input if available
            if user_input and not any(msg.get("content") == user_input and msg.get("role") == "user"
                                    for msg in messages):
                messages.append({"role": "user", "content": user_input})

            # Format tools for Anthropic
            formatted_tools = None
            if tools:
                formatted_tools = []
                for tool in tools:
                    formatted_tool = {
                        "name": tool["name"],
                        "description": tool["description"],
                        "input_schema": {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    }

                    # Add parameters
                    if "parameters" in tool:
                        for param_name, param_info in tool["parameters"].items():
                            formatted_tool["input_schema"]["properties"][param_name] = {
                                "type": param_info.get("type", "string"),
                                "description": param_info.get("description", "")
                            }

                            if param_info.get("required", False):
                                formatted_tool["input_schema"]["required"].append(param_name)

                    formatted_tools.append(formatted_tool)

            # Call the API
            response = self.client.messages.create(
                model=self.model,
                system=full_system_prompt,
                messages=messages,
                temperature=0.7,
                max_tokens=4000,
                tools=formatted_tools
            )

            # Check if the model wants to call a tool
            if hasattr(response, 'tool_calls') and response.tool_calls:
                # Return the tool calls for processing
                tool_calls = []
                for tool_call in response.tool_calls:
                    tool_calls.append({
                        "id": tool_call.id,
                        "name": tool_call.name,
                        "parameters": tool_call.input
                    })

                return {
                    "content": response.content[0].text if response.content else "",
                    "tool_calls": tool_calls
                }

            # Otherwise return just the content
            return response.content[0].text if response.content else ""

        except Exception as e:
            logger.error(f"Error generating Anthropic response: {str(e)}")
            return f"Error generating response: {str(e)}"
