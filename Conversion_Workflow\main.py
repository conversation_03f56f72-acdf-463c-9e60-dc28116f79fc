import sys
import uuid
from typing import Dict, Any, <PERSON><PERSON>
from config import Config<PERSON><PERSON><PERSON>
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM
)
from workflow import GraphBuilder
from dotenv import load_dotenv
load_dotenv()


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """Create an LLM instance based on the provider.

    Args:
        provider: The LLM provider name
        config_manager: The configuration manager

    Returns:
        An instance of the appropriate LLM class

    Raises:
        ValueError: If the provider is not supported
    """
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def setup_application() -> <PERSON><PERSON>[ConfigManager, Any]:
    """Set up the application with configuration and LLM.

    Returns:
        A tuple containing the configuration manager and LLM instance
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"Attempting to initialize {llm_provider} LLM...")
        llm = create_llm(llm_provider, config_manager)
    except Exception as e:
        print(f"Error initializing {llm_provider}: {str(e)}")
    return llm


def run_workflow(llm: Any) -> Dict[str, Any]:
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()
    # graph_builder.save_graph_image(graph_builder.graph)



    # source_code = """CREATE OR REPLACE PROCEDURE process_order(p_order_id IN NUMBER) AS
    # BEGIN
    #     -- Processing logic
    #     NULL;
    # EXCEPTION
    #     WHEN NO_DATA_FOUND THEN
    #         DBMS_OUTPUT.PUT_LINE('No data found');
    #     WHEN OTHERS THEN
    #         DBMS_OUTPUT.PUT_LINE('Error: ' || SQLERRM);
    # select * from employees where empno=401;
    # END;"""

    # target_code = """CREATE OR REPLACE FUNCTION process_order(p_order_id NUMERIC) RETURNS VOID AS $BODY$
    # BEGIN
    #     -- Processing logic
    #     NULL;
    # select * from siva;
    # EXCEPTION
    #     NO_DATA_FOUND THEN
    #         RAISE NOTICE 'No data found';
    #     WHEN OTHERS THEN
    #         RAISE NOTICE 'Error: %', SQLERRM;
    # END;
    # $BODY$ LANGUAGE plpgsql;"""


    # source_code = """
    # CREATE OR REPLACE  PROCEDURE "AHC"."P_ACTIVEAHCPATIENT"
    #                     (iv_UHID ahcpatient.uhid%TYPE,
    #                     oCursor_ActivePat OUT  SYS_REFCURSOR)

    # is
    # dummy number;
    # begin
    # dummy = select 1+2 from dual;
    # OPEN oCursor_ActivePat FOR
    # Select distinct ap.ahcno,ap.uhid, pvd.ahcid from patientvisitdetail pvd
    # left outer join patientvisits pv on pvd.ahcid=pv.ahcid
    # left outer join Ahcpatient ap on pv.ahcid=ap.ahcid
    # left outer join Queuemaster qm on qm.ahcid = pv.ahcid
    # left outer join patientpackage pp on pv.ahcid=pp.ahcid
    # Left outer join ScheduledPatients SP on sp.AHCID=pv.ahcid

    # where trunc(Sysdate)<= trunc(ScheduledDate+30)
    # and UPPER(qm.queuestatus) = 'PENDING'
    # and UPPER(pp.packagestatus) <> 'CANCELLED'
    # and UPPER(pv.visitstatus) <> 'CANCELLED'
    # and ap.uhid = iv_UHID;

    # /*select wft.approvalstatus ,wft.transactionid,wft.processid
    # from WorkFlowTransaction wft
    # left outer join  WorkFlowProcessMaster wfp on wft.processid = wfp.processid
    # where Upper(wft.approvalstatus) = 'APPROVED'
    # */



    # /*Select ap.ahcno,ap.reportcollected,pp.packageid
    # FROM AHCPATIENT AP
    # Left Outer Join Patientpackage PP ON pp.ahcid=ap.ahcid
    # where ap.reportcollected = 0 and ap.uhid=iv_UHID ;*/
    # dbms_output.put_line(SQL_CODE);
    # end P_ActiveAHCPatient;
    # """

    # target_code = """
    # CREATE OR REPLACE PROCEDURE ahc.P_ACTIVEAHCPATIENT (iv_UHID ahc.ahcpatient.uhid%TYPE, oCursor_ActivePat INOUT refcursor)
    # LANGUAGE plpgsql
    # SECURITY DEFINER
    # AS $BODY$
    # BEGIN
    #     SET search_path TO AHC;
    #     dummy := select 1+2;
    #     OPEN oCursor_ActivePat FOR SELECT DISTINCT
    #             ap.ahcno,
    #             ap.uhid,
    #             pvd.ahcid
    #         FROM
    #             ahc.patientvisitdetail pvd
    #         LEFT OUTER JOIN ahc.patientvisits pv ON pvd.ahcid = pv.ahcid
    #     LEFT OUTER JOIN ahc.Ahcpatient ap ON pv.ahcid = ap.ahcid
    #     LEFT OUTER JOIN ahc.Queuemaster qm ON qm.ahcid = pv.ahcid
    #     LEFT OUTER JOIN ahc.patientpackage pp ON pv.ahcid = pp.ahcid
    #     LEFT OUTER JOIN ahc.ScheduledPatients SP ON sp.AHCID = pv.ahcid
    # WHERE
    #     CURRENT_DATE <= ScheduledDate + 30::date
    #         AND UPPER(qm.queuestatus) = 'PENDING'
    #         AND UPPER(pp.packagestatus) <> 'CANCELLED'
    #         AND UPPER(pv.visitstatus) <> 'CANCELLED'
    #         AND ap.uhid = iv_UHID;

    #     /*
    # select wft.approvalstatus ,wft.transactionid,wft.processid
    # from WorkFlowTransaction wft
    # left outer join WorkFlowProcessMaster wfp on wft.processid = wfp.processid
    # where Upper(wft.approvalstatus) = 'APPROVED'
    #     */
    #     /*
    # Select ap.ahcno,ap.reportcollected,pp.packageid
    #     FROM AHCPATIENT AP
    #     Left Outer Join Patientpackage PP ON pp.ahcid=ap.ahcid
    # where ap.reportcollected = 0 and ap.uhid=iv_UHID ;
    #     */
    # raise notice '%%', SQLERRM;
    # END;
    # """

    # source_code = """
    # CREATE OR REPLACE  PROCEDURE "BILLING"."P_REP_DEPOSITRECEIPT_FC" (IV_OPPNO          IN PATIENTBILL.PATIENTIDENTIFIERNUMBER%TYPE := NULL,
    #                                                 IV_LOCATIONID     IN PATIENTBILL.LOCATIONID%TYPE,
    #                                                 ID_DATE           IN DATE,
    #                                                 IV_BILLNO         IN PATIENTBILL.BILLNO%TYPE := NULL,
    #                                                 IV_TRNUMBER       IN TRANSACTION.TRANSACTIONID%TYPE := NULL,
    #                                                 OC_DEPOSITRECEIPT OUT SYS_REFCURSOR,
    #                                                 OC_BILLDETAILS    OUT SYS_REFCURSOR) IS

    # V_REGISTRATIONNO TRANSACTION.REGISTRATIONNO%TYPE;
    # V_NONINRAMOUNT   TRANSACTION.TRANAMOUNT%TYPE :=0;
    # V_BILLNO         PATIENTBILL.BILLNO%TYPE;
    # V_PATIENTIPNO    PATIENTBILL.PATIENTIDENTIFIERNUMBER%TYPE;
    # V_chk            NUMBER;
    # V_COUNT          NUMBER;
    # V_INRAMOUNT      TRANSACTION.TRANAMOUNT%TYPE :=0;
    # /****************************************************************************************
    # /*****************************************************************************************
    # /*   File Description: This procedure billing.is to get deposit receipt details
    # /*   Description:
    # /*   Parameters:              iv_uhid               INPUT
    # /*                            iv_ipno               INPUT
    # /*                            in_receiptno          INPUT
    # /*                            oc_depositreceipt     OUTPUT
    # /*
    # /*   Returns:
    # /*   Date Created:  Aug 1, 2007
    # /*   Author:    Tata Consultancy Services LTD && Apollo Health city
    # /*   Change History:
    # /*   Date Modified:
    # /*   Changed By:
    # /*   Change Description:
    # /*****************************************************************************************
    # /******************************************************************************************/
    # BEGIN

    # IF IV_TRNUMBER IS NOT NULL THEN
    #     SELECT distinct TT.REGISTRATIONNO
    #     INTO V_REGISTRATIONNO
    #     FROM TRANSACTION TT
    #     WHERE TT.TRANTYPE = 'CR'
    #     AND TT.DELETEFLAG = 1
    #     AND TT.LOCATIONID = IV_LOCATIONID
    #     AND UPPER(TT.CURRENCYCODE) <> 'INR'
    #     AND TT.TRANSACTIONID = IV_TRNUMBER;
    #     V_chk := 1;
    # END IF;

    # IF IV_OPPNO IS NOT NULL THEN
    #     SELECT COUNT(rowid)
    #     INTO V_COUNT
    #     FROM PATIENTBILL PB
    #     WHERE ROWNUM = 1
    #     AND PB.PATIENTIDENTIFIERNUMBER = trim(IV_OPPNO);

    #     IF V_COUNT > 0 THEN
    #     SELECT PB.REGISTRATIONNO, PB.BILLNO, PB.PATIENTIDENTIFIERNUMBER
    #         INTO V_REGISTRATIONNO, V_BILLNO, V_PATIENTIPNO
    #         FROM PATIENTBILL PB
    #     WHERE ROWNUM = 1
    #         AND PB.PATIENTIDENTIFIERNUMBER = trim(IV_OPPNO);
    #     V_chk := 2;
    #     END IF;
    # END IF;

    # IF IV_BILLNO IS NOT NULL THEN
    #     SELECT PB.REGISTRATIONNO, PB.BILLNO, PB.PATIENTIDENTIFIERNUMBER
    #     INTO V_REGISTRATIONNO, V_BILLNO, V_PATIENTIPNO
    #     FROM PATIENTBILL PB
    #     WHERE PB.BILLNO = trim(IV_BILLNO);
    #     V_chk := 2;
    # END IF;

    # SELECT SUM(nvl(T.Fxtranamount,0)*nvl(T.Conversionrate,0))
    #     INTO V_NONINRAMOUNT
    #     FROM TRANSACTION T
    # WHERE UPPER(T.CURRENCYCODE) <> 'INR'
    #     AND T.TRANEVENT NOT IN (25, 49, 50, 114)
    #     AND T.TRANTYPE = 'CR'
    #     AND T.DELETEFLAG = 1
    #     AND T.LOCATIONID = IV_LOCATIONID
    #     AND (V_PATIENTIPNO IS NULL OR T.ACCOUNTNO = V_PATIENTIPNO)
    #     AND T.REGISTRATIONNO = V_REGISTRATIONNO
    #     AND TRUNC(T.TRANDATE) = TRUNC(ID_DATE);

    # SELECT SUM(nvl(T.TRANAMOUNT,0))
    #     INTO V_INRAMOUNT
    #     FROM TRANSACTION T
    # WHERE UPPER(T.CURRENCYCODE) = 'INR'
    #     AND T.TRANEVENT NOT IN (25, 49, 50, 114)
    #     AND T.TRANTYPE = 'CR'
    #     AND T.DELETEFLAG = 1
    #     AND T.LOCATIONID = IV_LOCATIONID
    #     AND (V_PATIENTIPNO IS NULL OR T.ACCOUNTNO = V_PATIENTIPNO)
    #     AND T.REGISTRATIONNO = V_REGISTRATIONNO
    #     AND TRUNC(T.TRANDATE) = TRUNC(ID_DATE);

    # OPEN OC_DEPOSITRECEIPT FOR
    #     SELECT DISTINCT TO_CHAR(T.TRANSACTIONID) AS RECEIPTNO,
    #                     T.REGISTRATIONNO AS UHIDNO,
    #                     T.TRANTYPE,
    #                     TRUNC(SYSDATE),
    #                     (SELECT (TM.TITLETYPE || ' ' || P.FIRSTNAME || ' ' ||
    #                             P.MIDDLENAME || ' ' || P.LASTNAME)
    #                     FROM REGISTRATION.PATIENT P, EHIS.TITLEMASTER TM
    #                     WHERE T.REGISTRATIONNO = P.UHID
    #                         AND TO_NUMBER(P.TITLE) = TM.TITLEID(+)) PATIENTNAME,
    #                     (SELECT DISTINCT (AD.STREET || '  ' || AD.LOCALITY || '  ' ||
    #                                     C.CITYNAME || '  ' || ST.STATENAME || '  ' ||
    #                                     CM.COUNTRYTYPE)
    #                     FROM REGISTRATION.ADDRESSMASTER AD,
    #                             REGISTRATION.PATIENT       IPM,
    #                             EHIS.STATEMASTER           ST,
    #                             EHIS.CITYMASTER            C,
    #                             EHIS.COUNTRYMASTER         CM
    #                     WHERE AD.REGISTRATIONID = IPM.REGISTRATIONID
    #                         AND C.CITYID = AD.CITY
    #                         AND ST.STATEID = AD.STATE
    #                         AND CM.COUNTRYID = AD.COUNTRY
    #                         AND IPM.UHID = T.REGISTRATIONNO
    #                         AND AD.ADDRESSTYPEID = 2) ADDRESS,
    #                     (SELECT P.AGE
    #                     FROM REGISTRATION.PATIENT P
    #                     WHERE T.REGISTRATIONNO = P.UHID) AGE,
    #                     (SELECT P.BIRTHDATE
    #                     FROM REGISTRATION.PATIENT P
    #                     WHERE T.REGISTRATIONNO = P.UHID) DATEOFBIRTH,
    #                     --sum(T.TRANAMOUNT) AS TRANAMOUNT,
    #                     T.TRANAMOUNT AS TRANAMOUNT,
    #                     DECODE(T.PATIENTSERVICEID, 2, 'OP', 3, 'IP', NULL) AS PATIENTSERVICENAME,
    #                     T.DESCRIPTION AS REMARK,
    #                     (SELECT DISTINCT TM.TITLETYPE || '' || HRM.FIRSTNAME || ' ' ||
    #                                     HRM.MIDDLENAME || ' ' || HRM.LASTNAME
    #                     FROM HR.EMPLOYEE_MAIN_DETAILS HRM,
    #                             EHIS.TITLEMASTER         TM
    #                     WHERE HRM.EMPLOYEEID = T.CREATEDBY
    #                         AND HRM.TITLEID = TM.TITLEID) OPERATOR,
    #                     T.CONVERSIONRATE,
    #                     T.CURRENCYCODE,
    #                     T.FXTRANAMOUNT
    #     FROM TRANSACTION T
    #     WHERE (V_PATIENTIPNO IS NULL OR T.ACCOUNTNO = V_PATIENTIPNO)
    #     AND (IV_TRNUMBER IS NULL OR T.TRANSACTIONID = IV_TRNUMBER)
    #     AND (V_BILLNO IS NULL OR T.BILLNO = V_BILLNO)
    #     AND T.TRANEVENT NOT IN (25, 49, 50, 114)
    #     AND T.LOCATIONID = IV_LOCATIONID
    #     AND T.DELETEFLAG = 1
    #     AND T.TRANTYPE = 'CR'
    #     AND UPPER(T.CURRENCYCODE) <> 'INR'
    #     AND T.REGISTRATIONNO = V_REGISTRATIONNO
    #     AND TRUNC(T.TRANDATE) = TRUNC(ID_DATE);

    # ---------------------------------------------------
    # IF V_chk = 2 THEN
    #     OPEN OC_BILLDETAILS FOR
    #     SELECT PB.PATIENTIDENTIFIERNUMBER,
    #             PB.REGISTRATIONNO AS UHID,
    #             PB.BILLDATE AS BILLDATE,
    #             PB.TOTALBILLAMOUNT,
    #             PB.DEPOSITUSED,
    #             (nvl(V_NONINRAMOUNT,0) + nvl(V_INRAMOUNT,0) - PB.TOTALBILLAMOUNT) AS REFUND,
    #             PB.BILLNO,
    #             F_RECEIPTNO_FC(V_PATIENTIPNO, IV_LOCATIONID, ID_DATE) AS RECEIPTNUMBER,
    #             /*PB.PASSPORTHOLDERNAME,
    #             PB.PASSPORTNO,
    #             PB.Nationality,*/
    #             (rp.firstname || ' ' || rp.middlename || ' ' || rp.lastname) AS PASSPORTHOLDERNAME,
    #         rpv.passportnumber AS PASSPORTNO,
    #         en.nationalitytype AS Nationality,
    #             PB.Patientpaidamount
    #         FROM PATIENTBILL PB left outer join registration.patient rp
    #         on rp.uhid = pb.registrationno
    #     left outer join registration.patientvisadetail rpv
    #         on rpv.registrationid = rp.registrationid
    #     left outer join ehis.nationalitymaster en
    #         on en.nationalityid = rpv.nationality
    #     and en.status = 1
    #     WHERE PB.DELFLAG = 1
    #         AND PB.PATIENTIDENTIFIERNUMBER = V_PATIENTIPNO
    #         AND PB.LOCATIONID = IV_LOCATIONID
    #         AND PB.BILLNO = V_BILLNO;

    # else
    #     OPEN OC_BILLDETAILS FOR
    #     SELECT tt.accountno AS PATIENTIDENTIFIERNUMBER,
    #         tt.registrationno AS UHID,
    #         TT.CREATEDDATE AS BILLDATE,
    #         0 AS TOTALBILLAMOUNT,
    #         0 AS DEPOSITUSED,
    #         0 AS REFUND,
    #         tt.billno as BILLNO,
    #         IV_TRNUMBER AS RECEIPTNUMBER,
    #         (rp.firstname || ' ' || rp.middlename || ' ' || rp.lastname) AS PASSPORTHOLDERNAME,
    #         rpv.passportnumber AS PASSPORTNO,
    #         en.nationalitytype AS Nationality,
    #         tt.tranamount AS Patientpaidamount
    #     FROM TRANSACTION TT
    #     left outer join registration.patient rp
    #     on rp.uhid = tt.registrationno
    #     left outer join registration.patientvisadetail rpv
    #     on rpv.registrationid = rp.registrationid
    #     left outer join ehis.nationalitymaster en
    #     on en.nationalityid = rpv.nationality
    #     and en.status = 1
    # WHERE ROWNUM = 1
    #     AND TT.TRANTYPE = 'CR'
    #     AND TT.DELETEFLAG = 1
    #     AND TT.LOCATIONID = IV_LOCATIONID
    #     AND UPPER(TT.CURRENCYCODE) <> 'INR'
    #     AND TT.TRANSACTIONID = IV_TRNUMBER;

    # end if;
    # /*EXCEPTION
    # WHEN OTHERS THEN
    #     DBMS_OUTPUT.PUT_LINE(SQLCODE || SQLERRM);*/

    # END P_REP_DEPOSITRECEIPT_FC;
    # """

    # target_code = """
    # set search_path to BILLING;
    # create
    # or replace procedure Billing.P_REP_DEPOSITRECEIPT_FC(IV_OPPNO IN Billing.PATIENTBILL.PATIENTIDENTIFIERNUMBER%TYPE default null,IV_LOCATIONID IN Billing.PATIENTBILL.LOCATIONID%TYPE default null,ID_DATE IN timestamp without time zone default null,IV_BILLNO IN Billing.PATIENTBILL.BILLNO%TYPE default null,IV_TRNUMBER IN Billing.TRANSACTION.TRANSACTIONID%TYPE default null,OC_DEPOSITRECEIPT inout refcursor default null,OC_BILLDETAILS inout refcursor default null)language plpgsql
    # security definer as $BODY$
    # declare
    # V_REGISTRATIONNO Billing.TRANSACTION.REGISTRATIONNO%TYPE;
    # V_NONINRAMOUNT Billing.TRANSACTION.TRANAMOUNT%TYPE := 0;
    # V_BILLNO Billing.PATIENTBILL.BILLNO%TYPE;
    # V_PATIENTIPNO Billing.PATIENTBILL.PATIENTIDENTIFIERNUMBER%TYPE;
    # V_chk numeric;
    # V_COUNT numeric;
    # V_INRAMOUNT Billing.TRANSACTION.TRANAMOUNT%TYPE := 0;
    # /*
    # ***************************************************************************************
    # ****************************************************************************************
    # File Description: This procedure Billing.is to get deposit receipt details
    # Description:
    # Parameters: iv_uhid INPUT
    # iv_ipno INPUT
    # in_receiptno INPUT
    # oc_depositreceipt OUTPUT
    # Returns:
    # Date Created: Aug 1, 2007
    # Author: Tata Consultancy Services LTD && Apollo Health city
    # Change History:
    # Date Modified:
    # Changed By:
    # Change Description:
    # ****************************************************************************************
    # ****************************************************************************************
    # */

    # BEGIN
    # IF IV_TRNUMBER IS NOT NULL THEN
    # SELECT distinct TT.REGISTRATIONNO
    # into strict V_REGISTRATIONNO
    # FROM Billing.TRANSACTION TT
    # where TT.TRANTYPE = 'CR'
    # and TT.DELETEFLAG = 1
    # and TT.LOCATIONID = IV_LOCATIONID
    # and UPPER(TT.CURRENCYCODE)<> 'INR'
    # and TT.TRANSACTIONID = IV_TRNUMBER;
    # V_chk := 1;
    # end if;
    # IF IV_OPPNO IS NOT NULL THEN
    # SELECT COUNT(ctid)into strict V_COUNT
    # FROM Billing.PATIENTBILL PB
    # limit 1
    # and PB.PATIENTIDENTIFIERNUMBER = trim(IV_OPPNO);
    # IF V_COUNT > 0 THEN
    # SELECT PB.REGISTRATIONNO, PB.BILLNO, PB.PATIENTIDENTIFIERNUMBER
    # into strict V_REGISTRATIONNO, V_BILLNO, V_PATIENTIPNO
    # FROM Billing.PATIENTBILL PB
    # limit 1
    # and PB.PATIENTIDENTIFIERNUMBER = trim(IV_OPPNO);
    # V_chk := 2;
    # END IF;
    # END IF;
    # IF IV_BILLNO IS NOT NULL THEN
    # SELECT PB.REGISTRATIONNO, PB.BILLNO, PB.PATIENTIDENTIFIERNUMBER
    # into strict V_REGISTRATIONNO, V_BILLNO, V_PATIENTIPNO
    # FROM Billing.PATIENTBILL PB
    # where PB.BILLNO = trim(IV_BILLNO);
    # V_chk := 2;
    # END IF;
    # SELECT SUM(nvl(T.Fxtranamount,0)*nvl(T.Conversionrate,0))into strict V_NONINRAMOUNT
    # FROM Billing.TRANSACTION T
    # where UPPER(T.CURRENCYCODE)<> 'INR'
    # and T.TRANEVENT NOT IN(25, 49, 50, 114)
    # and T.TRANTYPE = 'CR'
    # and T.DELETEFLAG = 1
    # and T.LOCATIONID = IV_LOCATIONID
    # and (V_PATIENTIPNO IS NULL
    # or T.ACCOUNTNO = V_PATIENTIPNO)
    # and T.REGISTRATIONNO = V_REGISTRATIONNO
    # and TRUNC(T.TRANDATE)= TRUNC(ID_DATE);
    # SELECT SUM(nvl(T.TRANAMOUNT,0))into strict V_INRAMOUNT
    # FROM Billing.TRANSACTION T
    # where UPPER(T.CURRENCYCODE)= 'INR'
    # and T.TRANEVENT NOT IN(25, 49, 50, 114)
    # and T.TRANTYPE = 'CR'
    # and T.DELETEFLAG = 1
    # and T.LOCATIONID = IV_LOCATIONID
    # and (V_PATIENTIPNO IS NULL
    # or T.ACCOUNTNO = V_PATIENTIPNO)
    # and T.REGISTRATIONNO = V_REGISTRATIONNO
    # and TRUNC(T.TRANDATE)= TRUNC(ID_DATE);
    # OPEN OC_DEPOSITRECEIPT FOR
    # SELECT DISTINCT TO_CHAR(T.TRANSACTIONID)AS RECEIPTNO,
    # T.REGISTRATIONNO AS UHIDNO,
    # T.TRANTYPE,
    # TRUNC(current_timestamp(0)::timestamp),(SELECT(TM.TITLETYPE || ' ' || P.FIRSTNAME || ' ' ||
    # P.MIDDLENAME || ' ' || P.LASTNAME)FROM REGISTRATION.PATIENT P, EHIS.TITLEMASTER TM
    # where T.REGISTRATIONNO = P.UHID
    # and TO_NUMBER(P.TITLE)= TM.TITLEID(+))PATIENTNAME,(SELECT DISTINCT(AD.STREET || ' ' || AD.LOCALITY || ' ' ||
    # C.CITYNAME || ' ' || ST.STATENAME || ' ' ||
    # CM.COUNTRYTYPE)FROM REGISTRATION.ADDRESSMASTER AD,
    # REGISTRATION.PATIENT IPM,
    # EHIS.STATEMASTER ST,
    # EHIS.CITYMASTER C,
    # EHIS.COUNTRYMASTER CM
    # where AD.REGISTRATIONID = IPM.REGISTRATIONID
    # and C.CITYID = AD.CITY
    # and ST.STATEID = AD.STATE
    # and CM.COUNTRYID = AD.COUNTRY
    # and IPM.UHID = T.REGISTRATIONNO
    # and AD.ADDRESSTYPEID = 2)ADDRESS,(SELECT P.AGE
    # FROM REGISTRATION.PATIENT P
    # where T.REGISTRATIONNO = P.UHID)AGE,(SELECT P.BIRTHDATE
    # FROM REGISTRATION.PATIENT P
    # where T.REGISTRATIONNO = P.UHID)DATEOFBIRTH,
    # --sum(T.TRANAMOUNT) AS TRANAMOUNT,


    # T.TRANAMOUNT AS TRANAMOUNT,
    # DECODE(T.PATIENTSERVICEID, 2, 'OP', 3, 'IP', NULL)AS PATIENTSERVICENAME,
    # T.DESCRIPTION AS REMARK,(SELECT DISTINCT TM.TITLETYPE || '' || HRM.FIRSTNAME || ' ' ||
    # HRM.MIDDLENAME || ' ' || HRM.LASTNAME
    # FROM HR.EMPLOYEE_MAIN_DETAILS HRM,
    # EHIS.TITLEMASTER TM
    # where HRM.EMPLOYEEID = T.CREATEDBY
    # and HRM.TITLEID = TM.TITLEID)OPERATOR,
    # T.CONVERSIONRATE,
    # T.CURRENCYCODE,
    # T.FXTRANAMOUNT
    # FROM Billing.TRANSACTION T
    # where (V_PATIENTIPNO IS NULL
    # or T.ACCOUNTNO = V_PATIENTIPNO)
    # and (IV_TRNUMBER IS NULL
    # or T.TRANSACTIONID = IV_TRNUMBER)
    # and (V_BILLNO IS NULL
    # or T.BILLNO = V_BILLNO)
    # and T.TRANEVENT NOT IN(25, 49, 50, 114)
    # and T.LOCATIONID = IV_LOCATIONID
    # and T.DELETEFLAG = 1
    # and T.TRANTYPE = 'CR'
    # and UPPER(T.CURRENCYCODE)<> 'INR'
    # and T.REGISTRATIONNO = V_REGISTRATIONNO
    # and TRUNC(T.TRANDATE)= TRUNC(ID_DATE);
    # --


    # IF V_chk = 2 THEN
    # OPEN OC_BILLDETAILS FOR
    # SELECT PB.PATIENTIDENTIFIERNUMBER,
    # PB.REGISTRATIONNO AS UHID,
    # PB.BILLDATE AS BILLDATE,
    # PB.TOTALBILLAMOUNT,
    # PB.DEPOSITUSED,(nvl(V_NONINRAMOUNT,0)+ nvl(V_INRAMOUNT,0)- PB.TOTALBILLAMOUNT)AS REFUND,
    # PB.BILLNO,
    # F_RECEIPTNO_FC(V_PATIENTIPNO, IV_LOCATIONID, ID_DATE)AS RECEIPTNUMBER,
    # /*
    # PB.PASSPORTHOLDERNAME,
    # PB.PASSPORTNO,
    # PB.Nationality,
    # */
    # (rp.firstname || ' ' || rp.middlename || ' ' || rp.lastname)AS PASSPORTHOLDERNAME,
    # rpv.passportnumber AS PASSPORTNO,
    # en.nationalitytype AS Nationality,
    # PB.Patientpaidamount
    # FROM Billing.PATIENTBILL PB
    # LEFT outer join registration.patient rp
    # on rp.uhid = pb.registrationno
    # LEFT outer join registration.patientvisadetail rpv
    # on rpv.registrationid = rp.registrationid
    # LEFT outer join ehis.nationalitymaster en
    # on en.nationalityid = rpv.nationality
    # and en.status = 1
    # where PB.DELFLAG = 1
    # and PB.PATIENTIDENTIFIERNUMBER = V_PATIENTIPNO
    # and PB.LOCATIONID = IV_LOCATIONID
    # and PB.BILLNO = V_BILLNO;
    # else
    # OPEN OC_BILLDETAILS FOR
    # SELECT tt.accountno AS PATIENTIDENTIFIERNUMBER,
    # tt.registrationno AS UHID,
    # TT.CREATEDDATE AS BILLDATE,
    # 0 AS TOTALBILLAMOUNT,
    # 0 AS DEPOSITUSED,
    # 0 AS REFUND,
    # tt.billno as BILLNO,
    # IV_TRNUMBER AS RECEIPTNUMBER,(rp.firstname || ' ' || rp.middlename || ' ' || rp.lastname)AS PASSPORTHOLDERNAME,
    # rpv.passportnumber AS PASSPORTNO,
    # en.nationalitytype AS Nationality,
    # tt.tranamount AS Patientpaidamount
    # FROM Billing.TRANSACTION TT
    # LEFT outer join registration.patient rp
    # on rp.uhid = tt.registrationno
    # LEFT outer join registration.patientvisadetail rpv
    # on rpv.registrationid = rp.registrationid
    # LEFT outer join ehis.nationalitymaster en
    # on en.nationalityid = rpv.nationality
    # and en.status = 1
    # limit 1
    # and TT.TRANTYPE = 'CR'
    # and TT.DELETEFLAG = 1
    # and TT.LOCATIONID = IV_LOCATIONID
    # and UPPER(TT.CURRENCYCODE)<> 'INR'
    # and TT.TRANSACTIONID = IV_TRNUMBER;
    # end if;
    # /*
    # EXCEPTION
    # WHEN OTHERS THEN
    # DBMS_OUTPUT.PUT_LINE(SQLCODE || SQLERRM);
    # */

    # end;
    # $BODY$;
    # """

    # deployment_error_message = 'ERROR: syntax error at or near "FRO"'
    # deployment_error_message = """ERROR: syntax error at or near "from"
    # LINE 1: select  from siva;"""
    # deployment_error_message = """ERROR: syntax error at or near "A"
    # LINE 4: SECURITY DEFINER"""

    # deployment_error_message = """
    #     too many parameters specified for RAISE
    # """

    # source_code = """
    # CREATE OR REPLACE  PROCEDURE "HL7"."P_SAVESUBDATATYPE" (v_SUBDATATYPELIST IN VARCHAR2)
    # AS
    # v_Row_Delimiter char(1);
    # v_Col_Delimiter char(1);
    # v_Optionality varchar2(50);
    # v_Col_DelimiterIndex int;
    # v_Row_DelimiterIndex int;
    # v_RowCount int;
    # v_SubDataType_List varchar2(1000);
    # v_SubDataTypeID varchar2(100);
    # tempString Varchar2(1000);
    # updateQuery Varchar2(2000);
    # v_Col_Delimiter2 int;

    # --------------------------------------------------------------------------------
    # -- Author : TCS
    # -- Created Date : 24-August-2007
    # -- Description : This Stored Procedure saves the subdatatype information.
    # --------------------------------------------------------------------------------
    # BEGIN

    # v_Row_Delimiter:= '|';
    # v_Col_Delimiter:= ',';

    # v_SubDataType_List:=v_SUBDATATYPELIST;

    # IF  LENGTH(TRIM(v_SubDataType_List)) != 0 THEN
    # loop
    #         select instr(v_SubDataType_List,'|') into v_Row_DelimiterIndex from dual;
    #         exit when v_Row_DelimiterIndex = 0;
    #         select instr(v_SubDataType_List,',') into v_Col_DelimiterIndex from dual;
    #         select substr(v_SubDataType_List, 0, v_Col_DelimiterIndex - 1) into v_SubDataTypeID from dual;
    #         tempString := Substr(v_SubDataType_List,v_Col_DelimiterIndex +1,Length(v_SubDataType_List) );
    #         v_Col_Delimiter2 := Instr(tempString,'|');
    #         v_Optionality := Substr(tempString,1,v_Col_Delimiter2 - 1);
    #         updateQuery := 'UPDATE HL7_STD_SUBDATATYPE SET OPTIONALITY = ''' || v_Optionality|| ''' WHERE SUBDATATYPEID=''' || v_SubDataTypeID || '''' ;
    #         dbms_output.put_line(SUBSTR(updateQuery,9,257));
    #         EXECUTE IMMEDIATE updateQuery;
    #         v_SubDataType_List := SUBSTR(v_SubDataType_List,v_Row_DelimiterIndex+1,Length(v_SubDataType_List));
    #         dbms_output.put_line('v_SubDataType_List= ' ||v_SubDataType_List);
    #         exit when v_SubDataType_List IS NULL;
    #         dbms_output.put_line(LENGTH(TRIM(v_SubDataType_List)));
    # end loop;
    # END IF;
    # END P_SAVESUBDATATYPE;
    # """

    # target_code = """
    #     SET search_path TO HL7;

    # CREATE OR REPLACE PROCEDURE hl7.P_SAVESUBDATATYPE (v_SUBDATATYPELIST IN varchar)
    # LANGUAGE plpgsql
    # SECURITY DEFINER
    # AS $BODY$
    # DECLARE
    #     v_Row_Delimiter char(1);
    #     v_Col_Delimiter char(1);
    #     v_Optionality varchar(50);
    #     v_Col_DelimiterIndex int;
    #     v_Row_DelimiterIndex int;
    #     v_RowCount int;
    #     v_SubDataType_List varchar(1000);
    #     v_SubDataTypeID varchar(100);
    #     tempString varchar(1000);
    #     updateQuery varchar(2000);
    #     v_Col_Delimiter2 int;
    #     --
    #     -- Author : TCS
    #     -- Created Date : 24-August-2007
    #     -- Description : This Stored Procedure saves the subdatatype information.
    #     --
    # BEGIN
    #     SET search_path TO HL7;
    #     v_Row_Delimiter := '|';
    #     v_Col_Delimiter := ',';
    #     v_SubDataType_List := v_SUBDATATYPELIST;
    #     IF LENGTH(TRIM(v_SubDataType_List)) != 0 THEN
    #         LOOP
    #             SELECT
    #                 public.instr (v_SubDataType_List, '|') INTO STRICT v_Row_DelimiterIndex;
    #             exit
    #             WHEN v_Row_DelimiterIndex = 0;
    #             SELECT
    #                 public.instr (v_SubDataType_List, ',') INTO STRICT v_Col_DelimiterIndex;
    #             SELECT
    #                 substr(v_SubDataType_List, 0, v_Col_DelimiterIndex - 1) INTO STRICT v_SubDataTypeID;
    #             tempString := Substr(v_SubDataType_List, v_Col_DelimiterIndex + 1, Length(v_SubDataType_List));
    #             v_Col_Delimiter2 := public.instr (tempString, '|');
    #             v_Optionality := Substr(tempString, 1, v_Col_Delimiter2 - 1);
    #             updateQuery := 'UPDATE hl7.HL7_STD_SUBDATATYPE SET OPTIONALITY = ''' || v_Optionality || ''' WHERE SUBDATATYPEID=''' || v_SubDataTypeID || '''';
    #             RAISE NOTICE '%', SUBSTR(updateQuery, 9, 257);
    #             EXECUTE updateQuery;
    #             v_SubDataType_List := SUBSTR(v_SubDataType_List, v_Row_DelimiterIndex + 1, Length(v_SubDataType_List));
    #             RAISE NOTICE 'v_SubDataType_List= %', v_SubDataType_List;
    #             exit
    #             WHEN v_SubDataType_List IS NULL;
    #             RAISE NOTICE '%', LENGTH(TRIM(v_SubDataType_List);
    #         END LOOP;
    #     END IF;
    # END;
    # $BODY$;
    # """

    source_code = """
    CREATE OR REPLACE  PROCEDURE "HL7"."P_SAVE_OUTMAPINFO"
    (
    v_RowDetails IN VARCHAR2,
    v_SegmentSequence IN VARCHAR2,
    v_crtdUser IN VARCHAR2
    )
    AS
        v_RowCount INT :=0;
        v_Row VARCHAR2(2000);
        v_RowTemp VARCHAR2(2000);
        v_MappingRows VARCHAR2(4000);
        commaPosition INT;
        tempString Varchar2(4000);
        stringLength INT := 0 ;
        counter INT := 0;
        pipePosition INT := 0;
        done BOOLEAN := true;
        segmentArr Varchar2(4000);
        columnSeparator Char(1);
        rowSeparator Char(1);
        v_CommaCount INT;
        v_ElementType Varchar2(1);
        v_NewLookupID Varchar2(10);

        v_COLMAPPINGID VARCHAR2(200);
        v_LOOKUPID  VARCHAR2(200);
        v_TABLENAME  VARCHAR2(200);
        v_COLUMNNAME  VARCHAR2(200);
        v_EVENTID VARCHAR2(36);
        v_SEGMENTID  VARCHAR2(200);
        v_APPCODE  VARCHAR2(200);
        v_MapSegID VARCHAR2(100);
        v_AliasName VARCHAR2(100);
        v_ColumnAliasName VARCHAR2(100);
        v_Repeats VARCHAR2(3);
        v_DBType VARCHAR2(20);
        v_DefaultValue VARCHAR2(50);
        v_Delimeter VARCHAR2(4);

        null_value_error EXCEPTION;

    --------------------------------------------------------------------------------
    -- Author : TCS
    -- Created Date : 21-August-2007
    -- Description : This procedure saves the rows in the outgoing mapping table.
    --------------------------------------------------------------------------------
    BEGIN
        --dbms_output.put_line('here');

        v_MappingRows := v_RowDetails;
        columnSeparator := ':';
        rowSeparator := '|';

        WHILE LENGTH(v_MappingRows) > 0
        LOOP


        pipePosition := INSTR(v_MappingRows, rowSeparator);

        v_Row := SUBSTR(v_MappingRows, 0, pipePosition - 1);

        -- Finding the Number of occurences of columnSeparator.
        v_CommaCount := LENGTH(v_Row) - LENGTH(TRANSLATE(v_Row, ' ' || columnSeparator, ' '));

        v_RowTemp := v_Row;

        --dbms_output.put_line(v_RowTemp);
        FOR i IN 1..v_CommaCount + 1
        LOOP


            commaPosition := INSTR(v_RowTemp, columnSeparator);

            IF commaPosition = 0 THEN
                tempString := v_RowTemp;
            ELSE
                tempString := SUBSTR(v_RowTemp, 0, commaPosition - 1);
            END IF;

            dbms_output.put_line('tempString = ' || tempString);

            IF i = 1 THEN
                v_COLMAPPINGID := tempString;
            ELSIF i = 2 THEN
                v_LOOKUPID := tempString;
            ELSIF i = 3 THEN
                v_TABLENAME := tempString;
            ELSIF i = 4 THEN
                v_COLUMNNAME := tempString;
            ELSIF i = 5 THEN
                v_EVENTID := tempString;
            ELSIF i = 6 THEN
                v_SEGMENTID := tempString;
            ELSIF i = 7 THEN
                v_APPCODE := tempString;
            ELSIF i = 8 THEN
                v_ElementType := tempString;
            ELSIF i = 9 THEN
                v_NewLookupID := tempString;
            ELSIF i = 11 THEN
                v_AliasName := tempString;
            ELSIF i = 10 THEN
                v_ColumnAliasName := tempString;
            ELSIF i=12 THEN
            v_Repeats := tempString;
            ELSIF i = 13 THEN
            v_DBType := tempString;
            ELSIF i = 14 THEN
            v_DefaultValue := tempString;
            ELSIF i=15 THEN
            v_Delimeter := tempString;
            END IF;

            IF (tempString IS NOT NULL) THEN
            --v_RowTemp := REPLACE(v_RowTemp, tempString || columnSeparator, '');
                v_RowTemp := SUBSTR(v_RowTemp, commaPosition+1, LENGTH(v_RowTemp));
            ELSE
                v_RowTemp := SUBSTR(v_RowTemp, 2, LENGTH(v_RowTemp));
            END IF;

            --dbms_output.put_line('v_RowTemp = ' || v_RowTemp);

        END LOOP;


        dbms_output.put_line('v_COLMAPPINGID = ' || v_COLMAPPINGID);
        dbms_output.put_line('v_LOOKUPID = ' || v_LOOKUPID);
        dbms_output.put_line('v_TABLENAME = ' || v_TABLENAME);
        dbms_output.put_line('v_COLUMNNAME = ' || v_COLUMNNAME);
        dbms_output.put_line('v_EVENTID = ' || v_EVENTID);
        dbms_output.put_line('v_SEGMENTID = ' || v_SEGMENTID);
        dbms_output.put_line('v_APPCODE = ' || v_APPCODE);
        dbms_output.put_line('v_ElementType = ' || v_ElementType);
        dbms_output.put_line('v_NewLookupID = ' || v_NewLookupID);
        dbms_output.put_line('v_AliasName = ' || v_AliasName);
        dbms_output.put_line('v_ColumnAliasName = ' || v_ColumnAliasName);
        dbms_output.put_line('v_Repeats = ' || v_Repeats);
        dbms_output.put_line('v_DBType = ' || v_DBType);
        dbms_output.put_line('v_DefaultValue = ' || v_DefaultValue);

        /*SELECT COUNT(1) INTO v_RowCount
        FROM HL7_MAP_OUT_COLUMN
        WHERE COLMAPPINGID = v_COLMAPPINGID;*/

        DELETE FROM HL7_MAP_OUT_COLUMN
        WHERE COLMAPPINGID = v_COLMAPPINGID;

        /*IF v_TABLENAME IS NULL OR v_TABLENAME = '' THEN
            RETURN;
        END IF;*/

        IF v_SEGMENTID IS NOT NULL THEN
            dbms_output.put_line('segment is not null');
            SELECT SEGMENTID INTO v_SEGMENTID
            FROM HL7_STD_SEGMENT
            WHERE SEGMENTCODE = v_SEGMENTID
            OR SEGMENTID = v_SEGMENTID;
        END IF;


        BEGIN
        dbms_output.put_line('here 3');
            SELECT INC.MAP_OUT_SEG_ID INTO v_MapSegID
            FROM HL7_MAP_OUT_SEGMENT INC
            WHERE INC.Segmentid = v_SEGMENTID
                    AND INC.Sequence_Segment = v_SegmentSequence
                    AND INC.PROFILEID = v_EVENTID;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                    v_MapSegID := '';
        END;

        IF (v_COLUMNNAME IS NULL) AND (v_TABLENAME IS NULL) AND (v_DefaultValue IS NULL)  THEN
            RETURN;
        ELSE
            dbms_output.put_line('here 1');
            INSERT INTO HL7_MAP_OUT_COLUMN
            (
            COLMAPPINGID
            ,LOOKUPID
            ,TABLENAME
            ,COLUMNNAME
            ,PROFILEID
            ,SEGMENTID
            ,ELEMENTTYPE
            ,NEWLOOKUPID
            ,MAP_OUT_SEG_ID
            ,ALIASNAME
            ,COLUMNALIASNAME
            ,REPETITIVE
            ,DBDATATYPE
            ,DEFAULTVALUE
            ,DELIMITER
            ,CRTD_USER_ID
            ,LAST_UPDT_USER_ID
            ,LAST_UPDT_DATE_TIME
            )
            VALUES
            (
            v_COLMAPPINGID
            ,v_LOOKUPID
            ,v_TABLENAME
            ,v_COLUMNNAME
            ,v_EVENTID
            ,v_SEGMENTID
            ,v_ElementType
            ,v_NewLookupID
            ,v_MapSegID
            ,v_AliasName
            ,v_ColumnAliasName
            ,v_Repeats
            ,v_DBType
            ,v_DefaultValue
            ,v_Delimeter
            ,v_CrtdUser
            ,v_CrtdUser
            ,sysdate
            );
            dbms_output.put_line('row inserted.');
        END IF;


        v_MappingRows := SUBSTR(v_MappingRows, pipePosition+1, Length(v_MappingRows));

        --dbms_output.put_line(v_MappingRows);

    END LOOP;

        EXCEPTION
        WHEN null_value_error THEN
            raise_application_error(-20121,'Encounterd null value for non nullable column.');
        WHEN OTHERS THEN
            raise_application_error(SQLCODE, 'Transaction in HL7_MAP_OUT_COLUMN failed.');


    END P_SAVE_OUTMAPINFO;
    """

    target_code = """
    SET search_path TO HL7;

    CREATE OR REPLACE PROCEDURE hl7.P_SAVE_OUTMAPINFO (v_RowDetails IN varchar, v_SegmentSequence IN varchar, v_crtdUser IN varchar)
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $BODY$
    DECLARE
        v_RowCount int := 0;
        v_Row varchar(2000);
        v_RowTemp varchar(2000);
        v_MappingRows varchar(4000);
        commaPosition int;
        tempString varchar(4000);
        stringLength int := 0;
        counter int := 0;
        pipePosition int := 0;
        done boolean := TRUE;
        segmentArr varchar(4000);
        columnSeparator char(1);
        rowSeparator char(1);
        v_CommaCount int;
        v_ElementType varchar(1);
        v_NewLookupID varchar(10);
        v_COLMAPPINGID varchar(200);
        v_LOOKUPID varchar(200);
        v_TABLENAME varchar(200);
        v_COLUMNNAME varchar(200);
        v_EVENTID varchar(36);
        v_SEGMENTID varchar(200);
        v_APPCODE varchar(200);
        v_MapSegID varchar(100);
        v_AliasName varchar(100);
        v_ColumnAliasName varchar(100);
        v_Repeats varchar(3);
        v_DBType varchar(20);
        v_DefaultValue varchar(50);
        v_Delimeter varchar(4);
        -- -- null_value_error EXCEPTION;
        --
        -- Author : TCS
        -- Created Date : 21-August-2007
        -- Description : This procedure saves the rows in the outgoing mapping table.
        --
        i record;
    BEGIN
        SET search_path TO HL7;
        --dbms_output.put_line('here');
        v_MappingRows := v_RowDetails;
        columnSeparator := ':';
        rowSeparator := '|';
        WHILE LENGTH(v_MappingRows) > 0 LOOP
            pipePosition := public.instr (v_MappingRows, rowSeparator);
            v_Row := SUBSTR(v_MappingRows, 0, pipePosition - 1);
            -- Finding the Number of occurences of columnSeparator.
            v_CommaCount := LENGTH(v_Row) - LENGTH(TRANSLATE(v_Row, ' ' || columnSeparator, ' '));
            v_RowTemp := v_Row;
            --dbms_output.put_line(v_RowTemp);
            FOR i IN 1..v_CommaCount + 1 LOOP
                commaPosition := public.instr (v_RowTemp, columnSeparator);
                IF commaPosition = 0 THEN
                    tempString := v_RowTemp;
                ELSE
                    tempString := SUBSTR(v_RowTemp, 0, commaPosition - 1);
                END IF;
                RAISE NOTICE 'tempString = %', tempString;
                IF i = 1 THEN
                    v_COLMAPPINGID := tempString;
                ELSIF i = 2 THEN
                    v_LOOKUPID := tempString;
                ELSIF i = 3 THEN
                    v_TABLENAME := tempString;
                ELSIF i = 4 THEN
                    v_COLUMNNAME := tempString;
                ELSIF i = 5 THEN
                    v_EVENTID := tempString;
                ELSIF i = 6 THEN
                    v_SEGMENTID := tempString;
                ELSIF i = 7 THEN
                    v_APPCODE := tempString;
                ELSIF i = 8 THEN
                    v_ElementType := tempString;
                ELSIF i = 9 THEN
                    v_NewLookupID := tempString;
                ELSIF i = 11 THEN
                    v_AliasName := tempString;
                ELSIF i = 10 THEN
                    v_ColumnAliasName := tempString;
                ELSIF i = 12 THEN
                    v_Repeats := tempString;
                ELSIF i = 13 THEN
                    v_DBType := tempString;
                ELSIF i = 14 THEN
                    v_DefaultValue := tempString;
                ELSIF i = 15 THEN
                    v_Delimeter := tempString;
                END IF;
                IF (tempString IS NOT NULL) THEN
                    --v_RowTemp := REPLACE(v_RowTemp, tempString || columnSeparator, '');
                    v_RowTemp := SUBSTR(v_RowTemp, commaPosition + 1, LENGTH(v_RowTemp));
                ELSE
                    v_RowTemp := SUBSTR(v_RowTemp, 2, LENGTH(v_RowTemp));
                END IF;
                --dbms_output.put_line('v_RowTemp = ' || v_RowTemp);
            END LOOP;
            RAISE NOTICE 'v_COLMAPPINGID = %', v_COLMAPPINGID;
            RAISE NOTICE 'v_LOOKUPID = %', v_LOOKUPID;
            RAISE NOTICE 'v_TABLENAME = %', v_TABLENAME;
            RAISE NOTICE 'v_COLUMNNAME = %', v_COLUMNNAME;
            RAISE NOTICE 'v_EVENTID = %', v_EVENTID;
            RAISE NOTICE 'v_SEGMENTID = %', v_SEGMENTID;
            RAISE NOTICE 'v_APPCODE = %', v_APPCODE;
            RAISE NOTICE 'v_ElementType = %', v_ElementType;
            RAISE NOTICE 'v_NewLookupID = %', v_NewLookupID;
            RAISE NOTICE 'v_AliasName = %', v_AliasName;
            RAISE NOTICE 'v_ColumnAliasName = %', v_ColumnAliasName;
            RAISE NOTICE 'v_Repeats = %', v_Repeats;
            RAISE NOTICE 'v_DBType = %', v_DBType;
            RAISE NOTICE 'v_DefaultValue = %', v_DefaultValue;

            /*
    SELECT COUNT(1) INTO v_RowCount
            FROM HL7_MAP_OUT_COLUMN
            WHERE COLMAPPINGID = v_COLMAPPINGID;
            */
            DELETE FROM hl7.HL7_MAP_OUT_COLUMN
            WHERE COLMAPPINGID = v_COLMAPPINGID;

            /*
    IF v_TABLENAME IS NULL OR v_TABLENAME = '' THEN
            RETURN;
            END IF;
            */
            IF v_SEGMENTID IS NOT NULL THEN
                RAISE NOTICE 'segment is not null';
                SELECT
                    SEGMENTID INTO STRICT v_SEGMENTID
                FROM
                    hl7.HL7_STD_SEGMENT
                WHERE
                    SEGMENTCODE = v_SEGMENTID
                    OR SEGMENTID = v_SEGMENTID;
            END IF;
            BEGIN
                RAISE NOTICE 'here 3';
                SELECT
                    INC.MAP_OUT_SEG_ID INTO STRICT v_MapSegID
                FROM
                    hl7.HL7_MAP_OUT_SEGMENT INC
                WHERE
                    INC.Segmentid = v_SEGMENTID
                    AND INC.Sequence_Segment = v_SegmentSequence
                    AND INC.PROFILEID = v_EVENTID;
            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    v_MapSegID := '';
            END;
            IF (v_COLUMNNAME IS NULL) AND (v_TABLENAME IS NULL) AND (v_DefaultValue IS NULL) THEN
                RETURN;
            ELSE
                RAISE NOTICE 'here 1';
                INSERT INTO hl7.HL7_MAP_OUT_COLUMN (COLMAPPINGID, LOOKUPID, TABLENAME, COLUMNNAME, PROFILEID, SEGMENTID, ELEMENTTYPE, NEWLOOKUPID, MAP_OUT_SEG_ID, ALIASNAME, COLUMNALIASNAME, REPETITIVE, DBDATATYPE, DEFAULTVALUE, DELIMITER, CRTD_USER_ID, LAST_UPDT_USER_ID, LAST_UPDT_DATE_TIME)
                    VALUES (v_COLMAPPINGID, v_LOOKUPID, v_TABLENAME, v_COLUMNNAME, v_EVENTID, v_SEGMENTID, v_ElementType, v_NewLookupID, v_MapSegID, v_AliasName, v_ColumnAliasName, v_Repeats, v_DBType, v_DefaultValue, v_Delimeter, v_CrtdUser, v_CrtdUser, current_timestamp(0)::timestamp);
                    RAISE NOTICE 'row inserted.';
                END IF;
                v_MappingRows := SUBSTR(v_MappingRows, pipePosition + 1, Length(v_MappingRows));
                --dbms_output.put_line(v_MappingRows);
        END LOOP;
    EXCEPTION
        WHEN null_value_error THEN
            raise_application_error (-20121, 'Encounterd null value for non nullable column.');
        WHEN OTHERS THEN
            raise_application_error (sqlstate, 'Transaction in HL7_MAP_OUT_COLUMN failed.');

    END;

    $BODY$;
    """

    # deployment_error_message = """
    #     ERROR:  syntax error at or near "."
    #     LINE 81: and .patientidentificationno(+)= pb.patientidentifiernumber
    # """

    # deployment_error_message = """
    #     mismatched parentheses at or near ";"
    # """

    deployment_error_message = """
        SQL Error [42704]: ERROR: unrecognized exception condition "null_value_error"
    """

    # mappings_statements = [{"source_statement":'CREATE OR REPLACE  PROCEDURE "GGDB2_TEST"."DELETE_CATEGORIES" (p_CATEGORYID NUMBER) as', 'target_statement':'CREATE OR REPLACE PROCEDURE Testpg.DELETE_CATEGORIES (p_CATEGORYID numeric) LANGUAGE plpgsql SECURITY DEFINER A'}, {'source_statement':'DELETE FROM GGDB2_TEST.CATEGORIES WHERE CATEGORYID = p_CATEGORYID;', 'target_statement':"DELETE FRO Testpg.CATEGORIES WHERE CATEGORYID = p_CATEGORYID;"},{'source_statement':'select * from siva;', 'target_statement':'select from siva;'},{'source_statement':'END;', 'target_statement':'END;$BODY$;'}]
    # mappings_statements = [{'source_statement':'CREATE OR REPLACE PROCEDURE process_order(p_order_id IN NUMBER)', 'target_statement':'CREATE OR REPLACE FUNCTION process_order(p_order_id NUMERIC) RETURNS VOID'},{'source_statement':'AS BEGIN NULL;', 'target_statement':'AS $$ BEGIN NULL;'},{'source_statement':"EXCEPTION WHEN NO_DATA_FOUND THEN DBMS_OUTPUT.PUT_LINE('No data found');", 'target_statement':"EXCEPTION NO_DATA_FOUND THEN RAISE NOTICE 'No data found';"}, {'source_statement':" WHEN OTHERS THEN DBMS_OUTPUT.PUT_LINE('Error: ' || SQLERRM);", 'target_statement': "WHEN OTHERS THEN RAISE NOTICE 'Error: %', SQLERRM;"}, {'source_statement':'END;', 'target_statement': "END; $$ LANGUAGE plpgsql;"}]

    # Create a unique thread ID for this workflow execution
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"Using thread ID: {thread_id}")

    # Pass the thread ID to the invoke_graph method
    # result = graph_builder.invoke_graph({
    #     "source_code": source_code,
    #     "target_code": target_code,
    #     "deployment_error_message": deployment_error_message,
    #     "mappings_statements": mappings_statements
    # }, thread_id=thread_id)
    result = graph_builder.invoke_graph({
        "source_code": source_code,
        "target_code": target_code,
        "deployment_error_message": deployment_error_message,
    }, thread_id=thread_id)
    return result


def main():
    try:
        llm = setup_application()
        result = run_workflow(llm)
        print("\nTarget Code:")
        print(result["target_code"])
        print("\nFixed Target Code:")
        print(result["fixed_code"])

        # Handle review results with better formatting
        if "review_results" in result:
            print("\nReview Results:")
            review = result["review_results"]
            print(f"Is properly fixed: {review['is_properly_fixed']}")
            print(f"Review Comments: {review['review_comments']}")
            print(f"Issues Found: {review['issues_found']}")
            print(f"Recommendations: {review['recommendations']}")

        print("\nExecution completed successfully!")

    except ValueError as e:
        print(f"\nConfiguration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
