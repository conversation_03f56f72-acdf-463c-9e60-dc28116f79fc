import os
import uuid
from typing import Dict, Any
from langgraph.graph import <PERSON><PERSON>raph, END, START
from langgraph.checkpoint.memory import MemorySaver
from state import WorkflowState
from nodes import Conversion_Flow_Nodes
from langchain_core.runnables.graph import MermaidDrawMethod



class GraphBuilder:
    def __init__(self, llm):
        self.llm = llm
        self.builder = StateGraph(WorkflowState)
        self.memory = MemorySaver()

    def build_graph(self):
        """
            Configure the graph by adding nodes, edges
        """
        # Create a new builder to avoid any cached nodes
        self.builder = StateGraph(WorkflowState)
        self.conversion_nodes = Conversion_Flow_Nodes(llm=self.llm)

        # Add the nodes for error identification, fixing, and review
        self.builder.add_node("identify_error_fix", self.conversion_nodes.identify_error_fix)
        self.builder.add_node("review_fixed_code", self.conversion_nodes.review_fixed_code)

        # Define the edges for the error fix flow
        self.builder.add_edge(START, "identify_error_fix")
        self.builder.add_edge("identify_error_fix", "review_fixed_code")
        self.builder.add_edge("review_fixed_code", END)

        # Other nodes (commented out for now)
        # self.builder.add_node("llm_node", self.conversion_nodes.llm_invoke)
        # self.builder.add_node("error_analysis", self.conversion_nodes.error_analysis_identify_pairs)
        # self.builder.add_node("sql_statements", self.conversion_nodes.sql_statements)
        # self.builder.add_node("sql_mappings", self.conversion_nodes.sql_mappings)
        # Edges
        # self.builder.add_edge(START, "llm_node")
        # self.builder.add_edge("llm_node", END)

        # For error analysis flow
        # self.builder.add_edge(START, 'error_analysis')
        # self.builder.add_edge('error_analysis', END)

        # For SQL statements flow
        # self.builder.add_edge(START, 'sql_statements')
        # self.builder.add_edge('sql_statements', 'sql_mappings')
        # self.builder.add_edge('sql_mappings', END)

        return self.builder

    def setup_graph(self):
        builder = self.build_graph()
        self.graph = builder.compile(
            interrupt_before=[], checkpointer=self.memory
        )
        return self.graph

    def invoke_graph(self, data: Dict[str, Any], thread_id: str = None) -> Dict[str, Any]:
        """
        Invoke the graph with the given input data.

        Args:
            input_data: Dictionary containing the input data for the workflow
            thread_id: Optional thread ID for the workflow execution

        Returns:
            Dictionary containing the workflow result
        """
        thread_id = thread_id or f"thread_{uuid.uuid4()}"
        thread = {"configurable": {"thread_id": thread_id}}
        return self.graph.invoke(data, config=thread)

    def save_graph_image(self, graph):
        try:
            # Generate the PNG image
            img_data = graph.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API
                )

            # # Generate the PNG image using Pyppeteer (local browser rendering)
            # img_data = graph.get_graph().draw_mermaid_png(
            #     draw_method=MermaidDrawMethod.PYPPETEER,
            #     max_retries=5,
            #     retry_delay=2.0
            # )

            # Save the image to a file
            graph_path = "workflow_graph.png"
            with open(graph_path, "wb") as f:
                f.write(img_data)

            print(f"Graph image saved to {graph_path}")
        except Exception as e:
            print(f"Warning: Could not generate graph image: {str(e)}")
            print("Continuing execution without graph visualization...")