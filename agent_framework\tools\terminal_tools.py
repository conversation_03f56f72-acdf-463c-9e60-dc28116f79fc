"""
Terminal Tools - Tools for interacting with the terminal
"""
import os
import sys
import subprocess
import logging
import time
import threading
import queue
from typing import Dict, List, Any, Optional, Union, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global process registry
_process_registry = {}
_next_terminal_id = 1
_registry_lock = threading.Lock()

class ProcessManager:
    """
    Manages processes launched by the agent
    """
    
    def __init__(self, terminal_id: int, process: subprocess.Popen, command: str, cwd: str):
        """
        Initialize the process manager
        
        Args:
            terminal_id: Unique ID for the terminal
            process: Subprocess object
            command: Command that was executed
            cwd: Working directory
        """
        self.terminal_id = terminal_id
        self.process = process
        self.command = command
        self.cwd = cwd
        self.output = ""
        self.is_running = True
        self.start_time = time.time()
        
        # Set up output capture
        self.output_queue = queue.Queue()
        self.output_thread = threading.Thread(target=self._capture_output)
        self.output_thread.daemon = True
        self.output_thread.start()
    
    def _capture_output(self) -> None:
        """Capture output from the process"""
        while self.is_running:
            try:
                if self.process.stdout:
                    line = self.process.stdout.readline()
                    if line:
                        line_str = line.decode('utf-8', errors='replace')
                        self.output += line_str
                        self.output_queue.put(line_str)
                    else:
                        # Check if process is still running
                        if self.process.poll() is not None:
                            self.is_running = False
                            break
                        time.sleep(0.1)
                else:
                    time.sleep(0.1)
            except Exception as e:
                logger.error(f"Error capturing output: {str(e)}")
                time.sleep(0.1)
    
    def read_output(self) -> str:
        """Read the current output"""
        return self.output
    
    def write_input(self, input_text: str) -> None:
        """
        Write input to the process
        
        Args:
            input_text: Text to write
        """
        if not self.is_running:
            raise ValueError(f"Process {self.terminal_id} is not running")
            
        if self.process.stdin:
            # Ensure input ends with newline
            if not input_text.endswith('\n'):
                input_text += '\n'
                
            self.process.stdin.write(input_text.encode('utf-8'))
            self.process.stdin.flush()
    
    def check_status(self) -> bool:
        """
        Check if the process is still running
        
        Returns:
            True if running, False otherwise
        """
        if self.is_running and self.process.poll() is not None:
            self.is_running = False
        return self.is_running
    
    def kill(self) -> None:
        """Kill the process"""
        if self.is_running:
            self.process.terminate()
            try:
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
            self.is_running = False

def launch_process(command: str, wait: bool, max_wait_seconds: int, cwd: Optional[str] = None) -> Dict[str, Any]:
    """
    Launch a new process
    
    Args:
        command: Command to execute
        wait: Whether to wait for the process to complete
        max_wait_seconds: Maximum time to wait (in seconds)
        cwd: Working directory (default: current directory)
        
    Returns:
        Process information
    """
    try:
        global _next_terminal_id
        
        # Use current directory if not specified
        if cwd is None:
            cwd = os.getcwd()
            
        logger.info(f"Launching process: {command} in {cwd}")
        
        # Create the process
        process = subprocess.Popen(
            command,
            shell=True,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            stdin=subprocess.PIPE,
            bufsize=1,
            universal_newlines=False
        )
        
        # Assign a terminal ID
        with _registry_lock:
            terminal_id = _next_terminal_id
            _next_terminal_id += 1
            
            # Create and register the process manager
            process_manager = ProcessManager(terminal_id, process, command, cwd)
            _process_registry[terminal_id] = process_manager
        
        # If wait is True, wait for the process to complete
        output = ""
        if wait:
            start_time = time.time()
            
            while process_manager.check_status():
                # Check if we've exceeded the maximum wait time
                if time.time() - start_time > max_wait_seconds:
                    logger.info(f"Process {terminal_id} exceeded max wait time, continuing in background")
                    break
                
                # Sleep briefly to avoid busy waiting
                time.sleep(0.1)
            
            # Read the output
            output = process_manager.read_output()
            
            # If the process has completed, remove it from the registry
            if not process_manager.check_status():
                with _registry_lock:
                    if terminal_id in _process_registry:
                        del _process_registry[terminal_id]
                        
                return {
                    "status": "completed",
                    "output": output,
                    "exit_code": process.returncode
                }
        
        # Return information about the process
        return {
            "status": "running" if process_manager.check_status() else "completed",
            "terminal_id": terminal_id,
            "command": command,
            "cwd": cwd,
            "output": output if wait else "Process started in background"
        }
    
    except Exception as e:
        error_msg = f"Error launching process: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "error": error_msg
        }

def read_process(terminal_id: int, wait: bool, max_wait_seconds: int) -> Dict[str, Any]:
    """
    Read output from a process
    
    Args:
        terminal_id: Terminal ID to read from
        wait: Whether to wait for the process to complete
        max_wait_seconds: Maximum time to wait (in seconds)
        
    Returns:
        Process output
    """
    try:
        # Check if the terminal exists
        if terminal_id not in _process_registry:
            return {
                "status": "error",
                "error": f"Terminal {terminal_id} not found"
            }
            
        process_manager = _process_registry[terminal_id]
        
        # If wait is True, wait for the process to complete
        if wait and process_manager.check_status():
            start_time = time.time()
            
            while process_manager.check_status():
                # Check if we've exceeded the maximum wait time
                if time.time() - start_time > max_wait_seconds:
                    break
                
                # Sleep briefly to avoid busy waiting
                time.sleep(0.1)
        
        # Read the output
        output = process_manager.read_output()
        
        # If the process has completed and wait was True, remove it from the registry
        if wait and not process_manager.check_status():
            with _registry_lock:
                if terminal_id in _process_registry:
                    del _process_registry[terminal_id]
        
        return {
            "status": "running" if process_manager.check_status() else "completed",
            "output": output,
            "exit_code": process_manager.process.returncode if not process_manager.check_status() else None
        }
    
    except Exception as e:
        error_msg = f"Error reading process: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "error": error_msg
        }

def write_process(terminal_id: int, input_text: str) -> Dict[str, Any]:
    """
    Write input to a process
    
    Args:
        terminal_id: Terminal ID to write to
        input_text: Text to write
        
    Returns:
        Result of the write operation
    """
    try:
        # Check if the terminal exists
        if terminal_id not in _process_registry:
            return {
                "status": "error",
                "error": f"Terminal {terminal_id} not found"
            }
            
        process_manager = _process_registry[terminal_id]
        
        # Check if the process is still running
        if not process_manager.check_status():
            return {
                "status": "error",
                "error": f"Process {terminal_id} is not running"
            }
            
        # Write the input
        process_manager.write_input(input_text)
        
        return {
            "status": "success",
            "message": f"Input written to process {terminal_id}"
        }
    
    except Exception as e:
        error_msg = f"Error writing to process: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "error": error_msg
        }

def kill_process(terminal_id: int) -> Dict[str, Any]:
    """
    Kill a process
    
    Args:
        terminal_id: Terminal ID to kill
        
    Returns:
        Result of the kill operation
    """
    try:
        # Check if the terminal exists
        if terminal_id not in _process_registry:
            return {
                "status": "error",
                "error": f"Terminal {terminal_id} not found"
            }
            
        process_manager = _process_registry[terminal_id]
        
        # Kill the process
        process_manager.kill()
        
        # Remove from registry
        with _registry_lock:
            if terminal_id in _process_registry:
                del _process_registry[terminal_id]
        
        return {
            "status": "success",
            "message": f"Process {terminal_id} killed"
        }
    
    except Exception as e:
        error_msg = f"Error killing process: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "error": error_msg
        }

def list_processes() -> Dict[str, Any]:
    """
    List all running processes
    
    Returns:
        List of processes
    """
    try:
        processes = []
        
        with _registry_lock:
            for terminal_id, process_manager in _process_registry.items():
                # Update status
                is_running = process_manager.check_status()
                
                processes.append({
                    "terminal_id": terminal_id,
                    "command": process_manager.command,
                    "cwd": process_manager.cwd,
                    "status": "running" if is_running else "completed",
                    "runtime": time.time() - process_manager.start_time
                })
        
        return {
            "status": "success",
            "processes": processes
        }
    
    except Exception as e:
        error_msg = f"Error listing processes: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "error": error_msg
        }
