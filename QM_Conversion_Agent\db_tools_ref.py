# from langchain_openai import AzureChatOpenAI
# from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
# from langchain_community.utilities import SQLDatabase
# from langchain.agents import AgentExecutor, create_openai_tools_agent
# from langchain import hub
# from langchain_core.tools import tool

# # Initialize the LLM
# llm = AzureChatOpenAI(
#     azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
#     azure_deployment="gpt4-deployment",
#     api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
#     api_version="2024-12-01-preview",
#     temperature=0.3,
# )
# print("LLM initialized")

# # Create a SQLDatabase connection
# # You can modify the connection string to match your database
# db = SQLDatabase.from_uri(
#     'postgresql+psycopg2://postgres:postgres@localhost/postgres'
# )
# print("Database connected")

# # Initialize the SQLDatabaseToolkit
# toolkit = SQLDatabaseToolkit(db=db, llm=llm)
# print("SQL toolkit created")

# # Define a custom tool for getting database tables
# @tool
# def get_database_size() -> dict:
#     """Get the total size of the database.

#     This tool returns the size of the database in megabytes.
#     Use this to check database storage usage.

#     Returns:
#         A dictionary with the database size information
#     """
#     # Use the existing db connection
#     result = db.run("""
#         SELECT
#             pg_size_pretty(pg_database_size(current_database())) as size,
#             pg_database_size(current_database()) as size_bytes
#     """)
#     return result

# @tool
# def get_database_tables():
#     """Get the list of tables in the database.

#     This tool directly connects to the database and returns all table names.
#     Use this when you need a simple list of tables without additional schema information.

#     Returns:
#         A list of dictionaries containing table names
#     """
#     # Use the existing db connection
#     result = db.run("""
#         SELECT table_name
#         FROM information_schema.tables
#         WHERE table_type = 'BASE TABLE'
#         AND table_schema NOT IN ('pg_catalog', 'information_schema');
#     """)
#     tables_list = [row.strip() for row in result.split('\n') if row.strip()]
#     return tables_list


# # Get the OpenAI functions agent prompt
# prompt = hub.pull("hwchase17/openai-functions-agent")

# # Define custom tools
# custom_tools = [get_database_size, get_database_tables]

# # Get all tools from the SQLDatabaseToolkit
# sql_tools = toolkit.get_tools()
# print(f"SQL toolkit tools: {len(sql_tools)} tools available")

# # Combine with custom tools
# all_tools = sql_tools + custom_tools
# print(f"Combined tools: {len(all_tools)} tools available")
# print(f"Combined tools: {[i.name for i in all_tools]}")

# # Create a new agent with all tools
# combined_agent = create_openai_tools_agent(llm, all_tools, prompt)
# agent_executor = AgentExecutor(
#     agent=combined_agent,
#     tools=all_tools,
#     verbose=True,
#     handle_parsing_errors=True,
#     max_iterations=10
# )
# print("Combined SQL agent created with both SQLDatabaseToolkit and custom tools")


# print("\n" + "-"*60)
# print("INTERACTIVE MODE INSTRUCTIONS:")
# print("-"*60)
# print("Type 'exit' or 'quit' to end the session")
# print("Type 'help' to see example queries")
# print("-"*60)

# # Start interactive loop
# while True:
#     user_input = input("\nEnter your database query: ")

#     # Handle special commands
#     if user_input.lower() in ['exit', 'quit']:
#         print("Exiting SQL agent...")
#         break

#     elif user_input.lower() in ['help', '?']:
#         print("\nExample queries you can try:")
#         print("1. List all tables in the database")
#         print("2. Show me the schema of the [table_name] table")
#         print("3. How many rows are in each table?")
#         print("4. What is the total size of the database?")
#         print("5. Find all records in [table_name] where [column] = [value]")
#         print("6. What are the largest tables in the database?")
#         print("7. Show me the first 5 rows from [table_name]")
#         print("8. Analyze the performance of the database")
#         continue

#     # Process normal queries
#     try:
#         print("\nProcessing your query...")
#         response = agent_executor.invoke({"input": user_input})
#         print("\nResponse:")
#         print(response["output"])
#     except Exception as e:
#         print(f"Error processing query: {str(e)}")
#         print("Try rephrasing your query or type 'help' for examples.")
















from langchain_openai import AzureChatOpenAI
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel, Field
from typing import Optional, List
import json
import re

# Define a Pydantic model for structured SQL responses
class SQLResponse(BaseModel):
    """Model for SQL agent responses.

    This model represents the structured output from the SQL agent,
    including the main answer, explanations, and formatted results.
    """
    answer: str = Field(
        description="The main answer to the user's question in a clear, concise format"
    )
    details: Optional[str] = Field(
        default=None,
        description="Additional explanation or context about the answer"
    )
    sql_code: Optional[str] = Field(
        default=None,
        description="SQL code that was generated or corrected as part of the response"
    )
    results: Optional[str] = Field(
        default=None,
        description="Formatted query results in a readable format"
    )
    recommendations: Optional[List[str]] = Field(
        default=None,
        description="Optional recommendations for the user based on their query"
    )

# Initialize the LLM
llm = AzureChatOpenAI(
    azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
    azure_deployment="gpt4-deployment",
    api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
    api_version="2024-12-01-preview",
    temperature=0.3,
)
print("LLM initialized")

# Create a SQLDatabase connection
# You can modify the connection string to match your database
db = SQLDatabase.from_uri('postgresql+psycopg2://qmig:Qu%40drant%40754@**********/qmigv2testdb?options=-c search_path=qmigrator_ai')

print("Database connected")

# Initialize the SQLDatabaseToolkit
toolkit = SQLDatabaseToolkit(db=db, llm=llm)
print("SQL toolkit created")

# Define a custom tool for getting database tables
@tool
def get_database_size() -> dict:
    """Get the total size of the database.

    This tool returns the size of the database in megabytes.
    Use this to check database storage usage.

    Returns:
        A dictionary with the database size information
    """
    # Use the existing db connection
    result = db.run("""
        SELECT
            pg_size_pretty(pg_database_size(current_database())) as size,
            pg_database_size(current_database()) as size_bytes
    """)
    return result

@tool
def get_database_tables():
    """Get the list of tables in the database.

    This tool directly connects to the database and returns all table names.
    Use this when you need a simple list of tables without additional schema information.

    Returns:
        A list of dictionaries containing table names
    """
    # Use the existing db connection
    result = db.run("""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_type = 'BASE TABLE'
        AND table_schema NOT IN ('pg_catalog', 'information_schema');
    """)
    return result

@tool
def get_table_row_count(table_name: str) -> dict:
    """Get the number of rows in a table.

    This tool returns the row count for a specific table.
    Use this to check the number of records in a table.

    Args:
        table_name: The name of the table to check

    Returns:
        A dictionary with the table name and row count
    """
    # Use the existing db connection
    result = db.run(f"SELECT COUNT(*) FROM {table_name}")
    return result


# Define custom tools
custom_tools = [get_database_size, get_database_tables, get_table_row_count]

# Get all tools from the SQLDatabaseToolkit
sql_tools = toolkit.get_tools()
print(f"SQL toolkit tools: {len(sql_tools)} tools available")

# Combine with custom tools
all_tools = sql_tools + custom_tools
print(f"Combined tools: {len(all_tools)} tools available")
print(f"Tool names: {[tool.name for tool in all_tools]}")

# Create a system prompt for the LLM
system_message = f"""
You are an expert SQL database analyst with access to a PostgreSQL database.
Your job is to help users understand the database structure, run queries, and analyze data.

IMPORTANT: You have direct access to the database through several tools. Always use the appropriate tools to answer questions about the database.

When answering questions, follow these guidelines:

1. ANSWER: Always provide a clear, direct answer to the user's question in 1-3 sentences.

2. DETAILS: When helpful, include additional context, explanations, or analysis of the database structure or query results.

3. SQL_CODE: When you generate, modify, or correct SQL code, always include the complete SQL code in a dedicated section. Format it using SQL code blocks with triple backticks.

4. RESULTS: Present query results in a well-formatted way. Use markdown tables for structured data when appropriate.

5. RECOMMENDATIONS: When relevant, suggest 1-3 follow-up queries or actions the user might want to take.

Format your responses for maximum readability:
- Use markdown formatting for headers, lists, and emphasis
- Present tables with clear headers and aligned columns
- Always put SQL code in ```sql code blocks
- Highlight important information
- Maintain conversation context by referring to previous queries when relevant
"""

# Create a chat prompt template
prompt = ChatPromptTemplate.from_messages([
    ("system", system_message),
    MessagesPlaceholder(variable_name="chat_history", optional=True),
    ("human", "{input}"),
    MessagesPlaceholder(variable_name="agent_scratchpad")
])

# Bind tools to the LLM
llm_with_tools = llm.bind_tools(all_tools)

# Create an agent with the tools
agent = create_openai_tools_agent(llm_with_tools, all_tools, prompt)

# Create the agent executor
agent_executor = AgentExecutor(
    agent=agent,
    tools=all_tools,
    verbose=True,
    handle_parsing_errors=True,
    max_iterations=10
)

# Create a structured output version of the LLM
structured_llm = llm.with_structured_output(SQLResponse)

print("\n" + "-"*80)
print("INTERACTIVE MODE INSTRUCTIONS:")
print("-"*80)
print("Type 'exit' or 'quit' to end the session")
print("Type 'help' to see example queries and SQL code examples")
print("Type 'clear' to clear conversation history")
print("Type 'history' to view conversation history")
print("SQL CODE: The agent will automatically extract and display SQL code when relevant")
print("-"*80)

# Initialize conversation history
chat_history = []

# Start interactive loop
while True:
    user_input = input("\nEnter your database query: ")

    # Handle special commands
    if user_input.lower() in ['exit', 'quit']:
        print("Exiting SQL agent...")
        break

    elif user_input.lower() in ['help', '?']:
        print("\nExample database queries you can try:")
        print("1. List all tables in the database")
        print("2. Show me the schema of the [table_name] table")
        print("3. How many rows are in each table?")
        print("4. What is the total size of the database?")
        print("5. Find all records in [table_name] where [column] = [value]")
        print("6. What are the largest tables in the database?")
        print("7. Show me the first 5 rows from [table_name]")
        print("8. Analyze the performance of the database")

        print("\nSQL code examples you can try:")
        print("1. Fix this SQL code: SELECT * FROM users WHERE id = 1 LIMIT")
        print("2. Create a stored procedure to update user information")
        print("3. Write a query to find duplicate records in a table")
        print("4. Optimize this query: SELECT * FROM large_table WHERE created_date > '2023-01-01'")
        print("5. Fix the mismatched parentheses in this SQL code: RAISE NOTICE '%', LENGTH(TRIM(v_SubDataType_List)")
        continue

    elif user_input.lower() == 'clear':
        chat_history = []
        print("Conversation history cleared.")
        continue

    elif user_input.lower() == 'history':
        if not chat_history:
            print("No conversation history yet.")
        else:
            print("\n" + "-"*60)
            print("CONVERSATION HISTORY:")
            print("-"*60)
            for i, message in enumerate(chat_history):
                role = "User" if isinstance(message, HumanMessage) else "AI"
                print(f"{role}: {message.content}")
            print("-"*60)
        continue

    # Process normal queries
    try:
        print("\nProcessing your query...")

        # Add user message to history
        user_message = HumanMessage(content=user_input)
        chat_history.append(user_message)

        # First, get the raw response from the agent
        raw_response = agent_executor.invoke({
            "input": user_input,
            "chat_history": chat_history
        })

        # Now, use the structured LLM to format the response nicely
        structured_prompt = f"""
        Based on the following database query and response, provide a structured answer:

        USER QUERY: {user_input}

        RAW RESPONSE: {raw_response["output"]}

        IMPORTANT INSTRUCTIONS FOR SQL CODE:
        1. If the response contains SQL code (usually in ```sql code blocks), extract it and include it in the sql_code field.
        2. Make sure to include the complete SQL code without any formatting or markdown.
        3. If there are multiple SQL code blocks, combine them into a single coherent SQL script.
        4. Remove any line numbers or other non-SQL content from the code.
        5. If the user is asking for SQL code or a fix to SQL code, this is high priority - make sure to extract it correctly.
        """

        # Get structured response using the Pydantic model
        structured_response = structured_llm.invoke(structured_prompt)

        # Add the formatted response to history
        ai_message = AIMessage(content=structured_response.answer)
        chat_history.append(ai_message)

        # Display the formatted response
        print("\n" + "="*80)

        # Always show the answer
        print("📌 ANSWER:")
        print(structured_response.answer)
        print()

        # Show details if available
        if structured_response.details:
            print("📋 DETAILS:")
            print(structured_response.details)
            print()

        # Show SQL code if available
        if structured_response.sql_code:
            print("💻 SQL CODE:")
            print("```sql")
            print(structured_response.sql_code)
            print("```")
            print()
        else:
            # Try to extract SQL code from results or raw response if not provided directly
            sql_code = None

            # First check in results
            if structured_response.results:
                sql_code_match = re.search(r'```sql\s*([\s\S]*?)\s*```', structured_response.results)
                if sql_code_match:
                    sql_code = sql_code_match.group(1).strip()
                else:
                    # Try to find any code block
                    code_block_match = re.search(r'```\w*\s*([\s\S]*?)\s*```', structured_response.results)
                    if code_block_match:
                        sql_code = code_block_match.group(1).strip()

            # If not found in results, check in raw response
            if not sql_code:
                sql_code_match = re.search(r'```sql\s*([\s\S]*?)\s*```', raw_response["output"])
                if sql_code_match:
                    sql_code = sql_code_match.group(1).strip()

            # Display extracted SQL code if found
            if sql_code:
                print("💻 SQL CODE:")
                print("```sql")
                print(sql_code)
                print("```")
                print()

        # Show results if available
        if structured_response.results:
            print("📊 RESULTS:")
            print(structured_response.results)
            print()

        # Show recommendations if available
        if structured_response.recommendations:
            print("💡 RECOMMENDATIONS:")
            for i, rec in enumerate(structured_response.recommendations, 1):
                print(f"{i}. {rec}")

        print("="*80)

    except Exception as e:
        print(f"Error processing query: {str(e)}")
        print("Try rephrasing your query or type 'help' for examples.")
