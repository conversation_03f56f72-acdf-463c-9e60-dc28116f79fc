from langchain_openai import AzureChatOpenAI
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel, <PERSON>
from typing import Optional, List
import json
import re

# Define a Pydantic model for structured SQL responses
class SQLResponse(BaseModel):
    """Model for SQL agent responses.

    This model represents the structured output from the SQL agent,
    including the main answer, explanations, and formatted results.
    """
    answer: str = Field(
        description="The main answer to the user's question in a clear, concise format"
    )
    details: Optional[str] = Field(
        default=None,
        description="Additional explanation or context about the answer"
    )
    sql_code: Optional[str] = Field(
        default=None,
        description="SQL code that was generated or corrected as part of the response"
    )
    results: Optional[str] = Field(
        default=None,
        description="Formatted query results in a readable format"
    )
    recommendations: Optional[List[str]] = Field(
        default=None,
        description="Optional recommendations for the user based on their query"
    )

# Initialize the LLM
llm = AzureChatOpenAI(
    azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
    azure_deployment="gpt4-deployment",
    api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
    api_version="2024-12-01-preview",
    temperature=0.3,
)
print("LLM initialized")

# Create a SQLDatabase connection
# You can modify the connection string to match your database
db = SQLDatabase.from_uri('postgresql+psycopg2://qmig:Qu%40drant%40754@**********/qmigv2testdb?options=-c search_path=qmigrator_ai')

print("Database connected")

# Initialize the SQLDatabaseToolkit
toolkit = SQLDatabaseToolkit(db=db, llm=llm)
print("SQL toolkit created")

# Define a custom tool for getting database tables
@tool
def get_database_size() -> dict:
    """Get the total size of the database.

    This tool returns the size of the database in megabytes.
    Use this to check database storage usage.

    Returns:
        A dictionary with the database size information
    """
    # Use the existing db connection
    result = db.run("""
        SELECT
            pg_size_pretty(pg_database_size(current_database())) as size,
            pg_database_size(current_database()) as size_bytes
    """)
    return result

@tool
def get_database_tables():
    """Get the list of tables in the database.

    This tool directly connects to the database and returns all table names.
    Use this when you need a simple list of tables without additional schema information.

    Returns:
        A list of dictionaries containing table names
    """
    # Use the existing db connection
    result = db.run("""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_type = 'BASE TABLE'
        AND table_schema NOT IN ('pg_catalog', 'information_schema');
    """)
    return result

@tool
def get_table_row_count(table_name: str) -> dict:
    """Get the number of rows in a table.

    This tool returns the row count for a specific table.
    Use this to check the number of records in a table.

    Args:
        table_name: The name of the table to check

    Returns:
        A dictionary with the table name and row count
    """
    # Use the existing db connection
    result = db.run(f"SELECT COUNT(*) FROM {table_name}")
    return result


# Define custom tools
custom_tools = [get_database_size, get_database_tables, get_table_row_count]

# Get all tools from the SQLDatabaseToolkit

sql_tools = toolkit.get_tools()
print(f"SQL toolkit tools: {len(sql_tools)} tools available")

# Combine with custom tools
all_tools = sql_tools + custom_tools
# print(f"Combined tools: {len(all_tools)} tools available")
# print(f"Tool names: {[tool.name for tool in all_tools]}")

# Create a system prompt for the LLM
system_message = f"""
You are an expert Oracle to PostgreSQL migration specialist with deep knowledge of both database systems.
Your job is to fix deployment errors in PostgreSQL code that was migrated from Oracle, ensuring the target code follows the source code logic correctly.

IMPORTANT: You have direct access to the database through several tools. Use these tools to analyze and fix migration issues.

When fixing migration issues, follow these guidelines:

1. ANALYZE ERROR: First, carefully analyze the deployment error message to understand the specific PostgreSQL syntax or feature that is causing the issue.

2. COMPARE SOURCE & TARGET: Compare the Oracle source code with the PostgreSQL target code to identify where the migration logic failed or needs adjustment.

3. FIX DEPLOYMENT ERRORS: Focus on fixing the immediate deployment errors first, ensuring the code will execute in PostgreSQL.

4. MAINTAIN LOGIC: Ensure that your fixes maintain the exact same business logic as the original Oracle code.

5. POSTGRESQL BEST PRACTICES: Apply PostgreSQL best practices while keeping the logic intact. Consider:
   - Data type differences between Oracle and PostgreSQL
   - Function name and parameter differences
   - Syntax variations for common operations
   - Sequence and identity column handling
   - Date/time function differences
   - NULL handling differences
   - Procedural code differences (PL/SQL vs PL/pgSQL)

6. SQL_CODE: Always include the complete fixed SQL code in a dedicated section. Format it using SQL code blocks with triple backticks.

7. EXPLANATION: Provide a clear explanation of what was changed and why, referencing specific Oracle vs PostgreSQL differences.

Format your responses for maximum readability:
- Use markdown formatting for headers, lists, and emphasis
- Present code comparisons in a clear way
- Always put SQL code in ```sql code blocks
- Highlight important changes
- Explain Oracle-specific constructs and their PostgreSQL equivalents
"""

# Create a chat prompt template
prompt = ChatPromptTemplate.from_messages([
    ("system", system_message),
    MessagesPlaceholder(variable_name="chat_history", optional=True),
    ("human", "{input}"),
    MessagesPlaceholder(variable_name="agent_scratchpad")
])

# Make sure we have tools to use
if not all_tools:
    print("Warning: No tools available. Using empty tools list.")
    all_tools = []

all_tools = []
# Bind tools to the LLM
llm_with_tools = llm.bind_tools(all_tools)

# Create an agent with the tools
agent = create_openai_tools_agent(llm_with_tools, all_tools, prompt)

# Create the agent executor
agent_executor = AgentExecutor(
    agent=agent,
    tools=all_tools,
    verbose=True,
    handle_parsing_errors=True,
    max_iterations=10
)

# Create a structured output version of the LLM
structured_llm = llm.with_structured_output(SQLResponse)

# Initialize conversation history
chat_history = []

def process_migration(source_code, target_code, deployment_error_message):
    """
    Process Oracle to PostgreSQL migration issues and fix deployment errors.

    Args:
        source_code (str): The original Oracle SQL/PL-SQL code
        target_code (str): The migrated PostgreSQL code with errors
        deployment_error_message (str): The error message from PostgreSQL deployment

    Returns:
        dict: A dictionary containing the structured response with fixed code and all output
    """
    try:
        # Initialize output collection
        output_lines = []
        output_lines.append("Processing migration issue...")

        # Construct a detailed prompt with all the migration information
        migration_prompt = f"""
        I need to fix a PostgreSQL deployment error in code migrated from Oracle.

        ORACLE SOURCE CODE:
        ```sql
        {source_code}
        ```

        POSTGRESQL TARGET CODE (WITH ERRORS):
        ```sql
        {target_code}
        ```

        DEPLOYMENT ERROR MESSAGE:
        {deployment_error_message}

        Please analyze the error, compare the source and target code, and provide a fixed version of the PostgreSQL code that:
        1. Resolves the deployment errors
        2. Maintains the same business logic as the Oracle source code
        3. Follows PostgreSQL best practices
        """

        # Add migration request to history
        user_message = HumanMessage(content=migration_prompt)
        chat_history.append(user_message)

        # Get the raw response from the agent
        raw_response = agent_executor.invoke({
            "input": migration_prompt,
            "chat_history": chat_history
        })

        # Format the response using the structured LLM
        structured_prompt = f"""
        Based on the following Oracle to PostgreSQL migration issue and response, provide a structured answer:

        ORACLE SOURCE CODE:
        ```sql
        {source_code}
        ```

        POSTGRESQL TARGET CODE (WITH ERRORS):
        ```sql
        {target_code}
        ```

        DEPLOYMENT ERROR MESSAGE:
        {deployment_error_message}

        RAW RESPONSE: {raw_response["output"]}

        IMPORTANT INSTRUCTIONS:
        1. In the 'answer' field, provide a concise summary of what was fixed.
        2. In the 'details' field, explain the Oracle vs PostgreSQL differences that caused the issue.
        3. In the 'sql_code' field, include ONLY the complete fixed PostgreSQL code without any markdown formatting.
        4. In the 'recommendations' field, suggest best practices for similar migrations in the future.
        """

        # Get structured response using the Pydantic model
        structured_response = structured_llm.invoke(structured_prompt)

        # Add the formatted response to history
        ai_message = AIMessage(content=structured_response.answer)
        chat_history.append(ai_message)

        # Extract SQL code if not directly provided
        if not structured_response.sql_code:
            sql_code = None

            # First check in results
            if structured_response.results:
                sql_code_match = re.search(r'```sql\s*([\s\S]*?)\s*```', structured_response.results)
                if sql_code_match:
                    sql_code = sql_code_match.group(1).strip()
                else:
                    # Try to find any code block
                    code_block_match = re.search(r'```\w*\s*([\s\S]*?)\s*```', structured_response.results)
                    if code_block_match:
                        sql_code = code_block_match.group(1).strip()

            # If not found in results, check in raw response
            if not sql_code:
                sql_code_match = re.search(r'```sql\s*([\s\S]*?)\s*```', raw_response["output"])
                if sql_code_match:
                    sql_code = sql_code_match.group(1).strip()

            # Update the structured response with extracted SQL code
            if sql_code:
                structured_response.sql_code = sql_code

        # Format the response and collect output
        output_lines.append("\n" + "="*80)
        output_lines.append("📌 FIXED MIGRATION ISSUE:")
        output_lines.append(structured_response.answer)
        output_lines.append("")

        if structured_response.details:
            output_lines.append("📋 ORACLE VS POSTGRESQL DIFFERENCES:")
            output_lines.append(structured_response.details)
            output_lines.append("")

        if structured_response.sql_code:
            output_lines.append("💻 FIXED POSTGRESQL CODE:")
            output_lines.append("```sql")
            output_lines.append(structured_response.sql_code)
            output_lines.append("```")
            output_lines.append("")

        if structured_response.results:
            output_lines.append("📊 ADDITIONAL INFORMATION:")
            output_lines.append(structured_response.results)
            output_lines.append("")

        if structured_response.recommendations:
            output_lines.append("💡 MIGRATION RECOMMENDATIONS:")
            for i, rec in enumerate(structured_response.recommendations, 1):
                output_lines.append(f"{i}. {rec}")

        output_lines.append("="*80)

        # Join all output lines into a single string
        full_output = "\n".join(output_lines)

        # Print all output at the end
        print(full_output)

        # Return the structured response with full output
        return {
            "summary": structured_response.answer,
            "explanation": structured_response.details,
            "fixed_code": structured_response.sql_code,
            "additional_info": structured_response.results,
            "recommendations": structured_response.recommendations,
            "full_output": full_output
        }

    except Exception as e:
        error_message = f"Error processing migration: {str(e)}"
        print(error_message)
        return {"error": error_message}

# Example usage (commented out to prevent execution when imported)
"""
Example usage:

source_code = '''
-- Oracle source code here
CREATE OR REPLACE PROCEDURE example_proc(p_id IN NUMBER) IS
BEGIN
  INSERT INTO target_table
  SELECT * FROM source_table
  WHERE id = p_id;
END;
'''

target_code = '''
-- PostgreSQL target code with errors
CREATE OR REPLACE PROCEDURE example_proc(p_id IN NUMERIC)
AS $$
BEGIN
  INSERT INTO target_table
  SELECT * FROM source_table
  WHERE id = p_id;
END;
$$ LANGUAGE plpgsql;
'''

error_message = "ERROR: syntax error at or near \"IN\""

result = process_migration(source_code, target_code, error_message)

# Access components of the result:
summary = result.get("summary")
explanation = result.get("explanation")
fixed_code = result.get("fixed_code")
recommendations = result.get("recommendations")
"""

# Only run this code if the script is executed directly (not imported)
if __name__ == "__main__":
    print("Oracle to PostgreSQL Migration Tool")
    print("Running example migration...")

    # Example inputs
    source_code = """
    -- Oracle source code here
    CREATE OR REPLACE PROCEDURE example_proc(p_id IN NUMBER) IS
    BEGIN
      INSERT INTO target_table
      SELECT * FROM source_table
      WHERE id = p_id;
    END;
    """

    target_code = """
    -- PostgreSQL target code with errors
    CREATE OR REPLACE PROCEDURE example_proc(p_id IN NUMERIC)
    AS $$
    BEGIN
      INSERT INTO target_table
      SELECT * FROM source_table
      WHERE id = p_id;
    END;
    $$ LANGUAGE plpgsql;
    """

    error_message = "ERROR: syntax error at or near \"IN\""

    # Process the migration
    result = process_migration(source_code, target_code, error_message)