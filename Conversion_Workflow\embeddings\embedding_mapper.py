"""
Embedding-based SQL statement mapping for Oracle to PostgreSQL conversion.

This module provides functionality to map SQL statements between Oracle and PostgreSQL
using embeddings and semantic search instead of LLM-based mapping.
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
from state.state import MappingStatement
import re
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer


class EmbeddingMapper:
    """Maps SQL statements using embeddings and semantic search.

    This class generates embeddings for SQL statements and uses cosine similarity
    to find the best matches between source and target statements.
    """

    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """Initialize the embedding mapper with a sentence transformer model.

        Args:
            model_name: Name of the sentence-transformers model to use
        """
        self.model = SentenceTransformer(model_name)
        self.similarity_threshold = 0.6  # Minimum similarity score to consider a match

    def generate_embeddings(self, statements: List[str]) -> np.ndarray:
        """Generate embeddings for a list of SQL statements.

        Args:
            statements: List of SQL statements to embed

        Returns:
            Numpy array of embeddings
        """
        # Clean statements before embedding
        cleaned_statements = [self._clean_statement(stmt) for stmt in statements]

        # Generate embeddings
        embeddings = self.model.encode(cleaned_statements, convert_to_numpy=True)
        return embeddings

    def _clean_statement(self, statement: str) -> str:
        """Clean a SQL statement for better embedding quality.

        Args:
            statement: SQL statement to clean

        Returns:
            Cleaned SQL statement
        """
        # Remove comments
        statement = re.sub(r'--.*?$', '', statement, flags=re.MULTILINE)
        statement = re.sub(r'/\*.*?\*/', '', statement, flags=re.DOTALL)

        # Normalize whitespace
        statement = re.sub(r'\s+', ' ', statement).strip()

        # Remove quotes from identifiers for better matching
        statement = re.sub(r'"([^"]*)"', r'\1', statement)

        return statement

    def _fix_sequential_numbering(self, mappings: List[MappingStatement],
                                source_statements: List[str],
                                target_statements: List[str]) -> List[MappingStatement]:
        """Fix the numbering of mappings to ensure sequential source and target numbers
        while maintaining the original order of statements.

        Args:
            mappings: List of MappingStatement objects
            source_statements: Original list of source statements
            target_statements: Original list of target statements

        Returns:
            List of MappingStatement objects with fixed sequential numbering
        """
        # Create a completely new approach that preserves the exact original order

        # Step 1: Identify which statements are mapped and which are not
        mapped_source_indices = set()
        mapped_target_indices = set()
        source_to_target_map = {}  # Maps source index to target index

        # First, find all mapped statements from the similarity-based mappings
        for mapping in mappings:
            if mapping.status == "Mapped":
                # Find the indices of these statements in the original lists
                source_idx = None
                target_idx = None

                for i, stmt in enumerate(source_statements):
                    if stmt == mapping.source_statement:
                        source_idx = i
                        break

                for i, stmt in enumerate(target_statements):
                    if stmt == mapping.target_statement:
                        target_idx = i
                        break

                if source_idx is not None and target_idx is not None:
                    mapped_source_indices.add(source_idx)
                    mapped_target_indices.add(target_idx)
                    source_to_target_map[source_idx] = target_idx

        # Step 2: Create a combined list of all statements in their original order
        # This ensures that target statements maintain their position even if source statements
        # at the same position are not mapped
        combined_statements = []

        # Track the maximum index processed so far
        max_idx = max(len(source_statements), len(target_statements)) - 1

        # Process statements in index order (0, 1, 2, ...)
        for idx in range(max_idx + 1):
            # Process source statement at this index if it exists
            if idx < len(source_statements):
                source_stmt = source_statements[idx]
                if idx in mapped_source_indices:
                    # This is a mapped statement
                    target_idx = source_to_target_map[idx]
                    combined_statements.append({
                        "type": "mapped",
                        "source_idx": idx,
                        "target_idx": target_idx,
                        "source_stmt": source_stmt,
                        "target_stmt": target_statements[target_idx]
                    })
                else:
                    # This is a source-only statement
                    combined_statements.append({
                        "type": "source_only",
                        "source_idx": idx,
                        "source_stmt": source_stmt
                    })

            # Process target statement at this index if it exists and is not already mapped
            if idx < len(target_statements) and idx not in mapped_target_indices:
                target_stmt = target_statements[idx]
                combined_statements.append({
                    "type": "target_only",
                    "target_idx": idx,
                    "target_stmt": target_stmt
                })

        # Step 3: Create the final mappings from the combined list
        result = []

        for item in combined_statements:
            if item["type"] == "mapped":
                result.append(MappingStatement(
                    source_statement=item["source_stmt"],
                    source_line_number=str(item["source_idx"] + 1),  # 1-based indexing
                    target_statement=item["target_stmt"],
                    target_line_number=str(item["target_idx"] + 1),  # 1-based indexing
                    status="Mapped"
                ))
            elif item["type"] == "source_only":
                result.append(MappingStatement(
                    source_statement=item["source_stmt"],
                    source_line_number=str(item["source_idx"] + 1),  # 1-based indexing
                    target_statement="",
                    target_line_number="",
                    status="Source Only"
                ))
            elif item["type"] == "target_only":
                result.append(MappingStatement(
                    source_statement="",
                    source_line_number="",
                    target_statement=item["target_stmt"],
                    target_line_number=str(item["target_idx"] + 1),  # 1-based indexing
                    status="Target Only"
                ))

        return result

    def map_statements(self, source_statements: List[str], target_statements: List[str]) -> List[MappingStatement]:
        """Map source statements to target statements using embeddings.

        Args:
            source_statements: List of Oracle SQL statements
            target_statements: List of PostgreSQL SQL statements

        Returns:
            List of MappingStatement objects with mappings between source and target
        """
        if not source_statements and not target_statements:
            return []

        # Generate embeddings for source and target statements
        source_embeddings = self.generate_embeddings(source_statements) if source_statements else np.array([])
        target_embeddings = self.generate_embeddings(target_statements) if target_statements else np.array([])

        # Initialize result list
        mappings = []

        # Calculate similarity matrix if both source and target have statements
        if len(source_statements) > 0 and len(target_statements) > 0:
            similarity_matrix = cosine_similarity(source_embeddings, target_embeddings)

            # Find the best matches
            for source_idx in range(len(source_statements)):
                # Find the best match for this source statement
                similarities = similarity_matrix[source_idx]
                best_match_idx = np.argmax(similarities)
                best_match_score = similarities[best_match_idx]

                # If similarity is above threshold, create a mapping
                if best_match_score >= self.similarity_threshold:
                    mappings.append(MappingStatement(
                        source_statement=source_statements[source_idx],
                        source_line_number=str(source_idx + 1),  # Original position
                        target_statement=target_statements[best_match_idx],
                        target_line_number=str(best_match_idx + 1),  # Original position
                        status="Mapped"
                    ))
                else:
                    # No good match found, this is a source-only statement
                    mappings.append(MappingStatement(
                        source_statement=source_statements[source_idx],
                        source_line_number=str(source_idx + 1),  # Original position
                        target_statement="",
                        target_line_number="",
                        status="Source Only"
                    ))
        else:
            # If no source or target statements, add them all as source-only or target-only
            for source_idx, stmt in enumerate(source_statements):
                mappings.append(MappingStatement(
                    source_statement=stmt,
                    source_line_number=str(source_idx + 1),
                    target_statement="",
                    target_line_number="",
                    status="Source Only"
                ))

        # Add any target statements that weren't mapped
        mapped_target_indices = set()
        for mapping in mappings:
            if mapping.status == "Mapped" and mapping.target_line_number:
                mapped_target_indices.add(int(mapping.target_line_number) - 1)  # Convert back to 0-based index

        for target_idx, stmt in enumerate(target_statements):
            if target_idx not in mapped_target_indices:
                mappings.append(MappingStatement(
                    source_statement="",
                    source_line_number="",
                    target_statement=stmt,
                    target_line_number=str(target_idx + 1),  # Original position
                    status="Target Only"
                ))

        # Fix the numbering to ensure sequential source and target numbers
        return self._fix_sequential_numbering(mappings, source_statements, target_statements)
