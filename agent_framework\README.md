# AI Coding Agent with Azure OpenAI

A flexible framework for building AI coding assistants using Azure OpenAI.

## Overview

This framework provides the core components needed to build an AI coding agent:

- **Agent Core**: Main agent logic for processing requests and coordinating tools
- **LLM Service**: Integration with Azure OpenAI
- **Tool Registry**: System for registering and executing tools
- **Memory Manager**: Persistent memory system for the agent
- **Built-in Tools**: File operations, code analysis, terminal interaction, and more

## Installation

1. Clone this repository
2. Install the required dependencies:

```bash
pip install openai requests beautifulsoup4
```

3. Set up your Azure OpenAI credentials as environment variables:

```bash
export OPENAI_API_KEY=your_api_key_here
export AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here
export AZURE_OPENAI_DEPLOYMENT=your_deployment_name_here
```

## Usage

### Running the Agent

The simplest way to run the agent is using the main.py script:

```bash
python main.py
```

You can also provide Azure OpenAI credentials as command-line arguments:

```bash
python main.py --api-key YOUR_KEY --endpoint YOUR_ENDPOINT --deployment YOUR_DEPLOYMENT
```

### Using the Agent in Your Code

```python
from core.agent import Agent
from core.llm_service import LLMService
from tools.tool_registry import ToolRegistry
from memory.memory_manager import MemoryManager
from tools.file_tools import read_file, save_file

# Create the LLM service with Azure OpenAI
llm_service = LLMService(
    provider="azure-openai",
    model="gpt-4",  # This will be used as the deployment name if azure_deployment is not provided
    azure_endpoint="https://your-resource-name.openai.azure.com/",
    azure_deployment="your-deployment-name",  # Optional, defaults to model name
    api_version="2023-05-15"  # Optional
)

# Create the tool registry and register tools
tool_registry = ToolRegistry()
tool_registry.register_tool(read_file)
tool_registry.register_tool(save_file)

# Create the memory manager
memory_manager = MemoryManager()

# Create the agent
agent = Agent(llm_service, tool_registry, memory_manager)

# Process a request
response = agent.process_request("Create a Python function that calculates the Fibonacci sequence")
print(response)
```

## Architecture

The framework follows a modular architecture:

1. **Agent**: Central component that orchestrates the workflow
2. **LLM Service**: Handles interactions with language models
3. **Tool Registry**: Manages available tools and their execution
4. **Memory Manager**: Stores and retrieves agent memories
5. **Tools**: Specialized functions for different tasks

## Extending the Framework

### Adding New Tools

```python
def my_custom_tool(param1: str, param2: int = 10) -> str:
    """
    Description of what the tool does

    Args:
        param1: Description of param1
        param2: Description of param2

    Returns:
        Result of the tool execution
    """
    # Tool implementation
    return f"Processed {param1} with parameter {param2}"

# Register the tool
tool_registry.register_tool(
    my_custom_tool,
    description="Custom tool that does something useful",
    parameter_descriptions={
        "param1": "Detailed description of param1",
        "param2": "Detailed description of param2"
    }
)
```

## Available Tools

The framework includes several built-in tools:

- **File Tools**: save_file, read_file, list_files, edit_file, remove_files
- **Code Tools**: search_code, analyze_code_structure, codebase_retrieval
- **Terminal Tools**: launch_process, read_process, write_process, kill_process, list_processes

## License

MIT License

## Acknowledgements

This framework is inspired by Augment and other AI agent architectures, and is designed to be extensible for different use cases.
