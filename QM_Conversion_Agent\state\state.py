from pydantic import BaseModel, Field
from typing import Optional, List, Literal


# Structured Output Models for AI Responses
class StatementMappingItem(BaseModel):
    """Model for a single statement mapping."""
    source_line: int = Field(description="Source statement line number")
    target_line: int = Field(description="Target statement line number")
    confidence: float = Field(description="Confidence score between 0.0 and 1.0")
    reason: str = Field(description="Reason for this mapping")


class MappingOutput(BaseModel):
    """Model for mapping AI output."""
    mappings: List[StatementMappingItem] = Field(description="List of statement mappings")


class WrongMappingItem(BaseModel):
    """Model for a wrong mapping identification."""
    source_line: int = Field(description="Source statement line number")
    target_line: int = Field(description="Target statement line number")
    reason: str = Field(description="Reason why this mapping is wrong")


class WrongMappingOutput(BaseModel):
    """Model for wrong mapping identification AI output."""
    wrong_mappings: List[WrongMappingItem] = Field(description="List of wrong mappings")


class StatementMapping(BaseModel):
    """Mapping between source and target statements."""
    source_statement: str = Field(description="The source statement")
    source_line_number: int = Field(description="The line number of the source statement (1-based)")
    target_statement: str = Field(description="The target statement")
    target_line_number: int = Field(description="The line number of the target statement (1-based)")
    status: Literal["Matched", "Source Only", "Target Only", "Target Only (Duplicate)"] = Field(
        description="The status of the mapping: Matched, Source Only, Target Only, or Target Only (Duplicate)"
    )


class WorkflowState(BaseModel):
    """Workflow state for code processing pipeline."""
    input: str = Field(
        description="Original Source code to be processed"
    )
    output: Optional[str] = Field(
        default=None,
        description="Code after documentation has been added"
    )
    source_code: str = Field(
        description="Original Source code to be processed"
    )
    target_code: str = Field(
        description="Original Target code to be processed"
    )
    source_statements: Optional[List[str]] = Field(
        default=None,
        description="Source SQL statements after splitting"
    )
    target_statements: Optional[List[str]] = Field(
        default=None,
        description="Target SQL statements after splitting"
    )
    mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="Mappings between source and target statements"
    )
    enhanced_mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="Enhanced mappings between source and target statements after cross-batch matching"
    )
    verified_mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="Verified mappings after checking for unmatched statements and attempting to map them"
    )
    validated_mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="Validated mappings after quality checks and potential re-mapping"
    )
    validation_passed: Optional[bool] = Field(
        default=None,
        description="Whether the mapping validation passed"
    )
    validation_issues: Optional[List[str]] = Field(
        default=None,
        description="List of validation issues found during mapping validation"
    )
    wrong_mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="List of wrong mappings identified during validation"
    )
    correction_attempts: Optional[int] = Field(
        default=0,
        description="Number of correction attempts made"
    )
    cleaned_mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="Mappings after duplicate removal"
    )
    duplicates_removed: Optional[int] = Field(
        default=0,
        description="Number of duplicate mappings removed"
    )
    sorted_mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="Final sorted mappings by line numbers"
    )
    final_excel_saved: Optional[bool] = Field(
        default=False,
        description="Whether final sorted Excel file was saved"
    )


class CodeOutput(BaseModel):
    output: str = Field(description="The documented code")

