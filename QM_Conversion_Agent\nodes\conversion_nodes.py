from state import WorkflowState, StatementMapping
from typing import Dict, Any
from formatting.sql_splitter import split_sql_statements
import pandas as pd
import os


class UniversalCodeMigrationNodes:
    def __init__(self, llm):
        self.llm = llm

    def splitStatements(self, state: WorkflowState) -> Dict[str, Any]:
        """Split ANY code into individual statements - works with any programming language or database."""
        try:
            # Get the source and target code from the state
            source_code = state.source_code
            target_code = state.target_code

            # Split the source and target code into statements
            source_statements = split_sql_statements(source_code)
            target_statements = split_sql_statements(target_code)

            # Create dataframes for source and target statements with numbering starting from 1
            source_df = pd.DataFrame({
                "Statement Number": range(1, len(source_statements) + 1),
                "Statement": source_statements
            })

            target_df = pd.DataFrame({
                "Statement Number": range(1, len(target_statements) + 1),
                "Statement": target_statements
            })

            # Create mappings directory if it doesn't exist
            mappings_dir = "mappings"
            os.makedirs(mappings_dir, exist_ok=True)

            # Save dataframes to Excel file with separate sheets inside mappings folder
            excel_path = os.path.join(mappings_dir, "statements.xlsx")
            with pd.ExcelWriter(excel_path) as writer:
                source_df.to_excel(writer, sheet_name="Source Statements", index=False)
                target_df.to_excel(writer, sheet_name="Target Statements", index=False)

            print(f"Saved statements to {os.path.abspath(excel_path)}")

            # Return the updated state
            return {
                "source_statements": source_statements,
                "target_statements": target_statements
            }
        except Exception as e:
            print(f"Error in splitStatements node: {str(e)}")
            return {
                "source_statements": [],
                "target_statements": []
            }



    def mapStatements(self, state: WorkflowState) -> Dict[str, Any]:
        """🤖 AI-based code mapping with Oracle to PostgreSQL context - confidence 0.75 with tie-breaking."""
        try:
            print("🚀 Starting AI-powered code mapping...")

            # Get statements
            source_statements = state.source_statements or []
            target_statements = state.target_statements or []

            if not source_statements or not target_statements:
                print("⚠️ No statements to map")
                return {"mappings": []}

            print(f"📊 Processing {len(source_statements)} source → {len(target_statements)} target statements")

            # 🎯 Pure AI Intelligence - No hardcoded rules
            mappings = self.pureAiMapping(source_statements, target_statements)

            # 🎯 Save results
            self.saveMappingsToFile(mappings)

            print(f"✅ Universal AI mapping complete: {len(mappings)} total mappings")
            return {"mappings": mappings}

        except Exception as e:
            print(f"❌ Error in universal AI mapping: {str(e)}")
            return {"mappings": []}

    def pureAiMapping(self, source_statements, target_statements):
        """🧠 Pure AI intelligence for code mapping with Oracle to PostgreSQL context."""
        print("🧠 Invoking AI for code conversion analysis...")

        # Create UNIVERSAL DEEP UNDERSTANDING mapping prompt for any converted code
        prompt = f"""You are an expert Oracle to PostgreSQL migration specialist with deep understanding of database code conversion patterns.

CRITICAL CONTEXT: The target code is ALREADY a converted PostgreSQL version of the source Oracle code. Your task is to map equivalent statements between the original Oracle source code and the converted PostgreSQL target code.

TASK: Map {len(source_statements)} source statements to {len(target_statements)} target statements by identifying which target statements correspond to which source statements in the conversion.

SOURCE STATEMENTS (ORIGINAL):
{chr(10).join([f"{i+1:3d}. {stmt.strip()}" for i, stmt in enumerate(source_statements)])}

TARGET STATEMENTS (CONVERTED VERSION):
{chr(10).join([f"{i+1:3d}. {stmt.strip()}" for i, stmt in enumerate(target_statements)])}

DEEP UNDERSTANDING APPROACH:
The target code is a converted version of the source code. You need to:
1. Understand the conversion patterns used
2. Identify which target statements represent which source statements
3. Map based on logical equivalence in the conversion process
4. Account for syntax transformations during conversion

UNIVERSAL CONVERSION PATTERN RECOGNITION:
- Procedure/function declarations with syntax changes
- Variable declarations with data type conversions
- Control flow statements with syntax adaptations
- Data operations with function/operator translations
- Exception handling with different syntax
- Built-in functions replaced with equivalents
- Syntax transformations while preserving logic

INTELLIGENT MAPPING STRATEGY:
1. Identify the PURPOSE of each source statement
2. Find the target statement that serves the SAME PURPOSE in the converted code
3. Consider that syntax may be different but functionality is equivalent
4. Map based on logical flow and functional equivalence
5. Understand that some statements may be restructured during conversion

UNIVERSAL MAPPING PRINCIPLES FOR CONVERTED CODE:
1. Focus on FUNCTIONAL EQUIVALENCE rather than exact syntax
2. Understand that the target code implements the same logic as source
3. Map based on what each statement ACCOMPLISHES in the overall flow
4. Consider conversion transformations (data types, functions, syntax)
5. Identify corresponding statements even if syntax differs

PURE LLM INTELLIGENCE APPROACH:
- Use DEEP AI UNDERSTANDING to analyze functional equivalence
- Apply SEMANTIC REASONING to identify corresponding statements
- Use CONTEXTUAL INTELLIGENCE to understand conversion patterns
- Apply LOGICAL ANALYSIS to determine statement relationships
- NO CONFIDENCE SCORES - Pure LLM decision making

CRITICAL UNDERSTANDING:
The target code is NOT random - it's a converted version of the source code. Your job is to identify which converted target statement corresponds to which original source statement, understanding that:
- Syntax will be different due to language/system differences
- Some statements may be restructured but serve the same purpose
- Data types and functions will be converted to target equivalents
- The overall logic flow should be preserved

GENERIC CONVERSION PATTERNS:
- Function/procedure declarations with different syntax
- Variable declarations with converted data types
- Control structures with adapted syntax
- Data operations with translated functions
- Exception handling with different mechanisms
- Built-in functions replaced with target equivalents

OUTPUT JSON FORMAT:
{{
  "mappings": [
    {{
      "source_line": 1,
      "target_line": 1,
      "reason": "LLM identified functional equivalence through semantic analysis",
      "status": "Matched"
    }}
  ]
}}

CRITICAL ANTI-BIAS INSTRUCTIONS - PURE CONTENT-BASED MAPPING:
- NEVER map based on line number proximity (SX→TX is NOT automatically correct)
- Line numbers are MISLEADING - statements may be shifted in converted code
- Focus ONLY on statement content and functionality
- Search through ALL target statements to find functional equivalence
- Ignore positional similarity completely

UNIVERSAL MAPPING PRINCIPLES:
- Map by SEMANTIC EQUIVALENCE, not line number patterns
- Variable assignments map to equivalent variable assignments
- Control structures map to equivalent control structures
- Function calls map to equivalent function calls
- Data operations map to equivalent data operations
- Declarations map to equivalent declarations

GENERIC CONTENT ANALYSIS APPROACH:
- Read the ACTUAL TEXT and PURPOSE of each source statement
- Search through ALL target statements to find the same PURPOSE
- Understand that syntax may differ but functionality is preserved
- Map based on what the statement ACCOMPLISHES, not where it appears
- Account for statement reordering during conversion

UNIVERSAL ANTI-BIAS STRATEGY:
1. Analyze source statement PURPOSE and FUNCTIONALITY
2. Search through ALL target statements (ignore line positions)
3. Find target statement with SAME or EQUIVALENT functionality
4. Map based on semantic correspondence, not positional patterns
5. Understand that converted code may have different statement ordering

ORACLE TO POSTGRESQL MAPPING VALIDATION:
- Do the statements serve the same business purpose?
- Do they accomplish the same logical operation?
- Are they functionally equivalent in Oracle to PostgreSQL conversion?
- Would a developer expect these statements to correspond?
- IGNORE case sensitivity differences (true vs TRUE, varchar vs VARCHAR)
- IGNORE syntax variations that preserve functionality
- Focus on semantic equivalence, not syntactic exactness
- Consider Oracle to PostgreSQL conversion patterns and transformations

LLM DECISION CRITERIA:
- If statements serve the SAME PURPOSE → Include in output with status "Matched"
- If statements are functionally EQUIVALENT → Include in output with status "Matched"
- If statements do DIFFERENT things → DO NOT include in output
- Use PURE LLM INTELLIGENCE to determine functional equivalence
- NO confidence scores needed - LLM makes the decision
- IGNORE line number proximity - map by FUNCTIONALITY ONLY
- Work with ANY programming language or system conversion"""

        try:
            # 🤖 Invoke AI using structured output
            from state import MappingOutput

            structured_llm = self.llm.client.with_structured_output(MappingOutput)
            ai_result = structured_llm.invoke(prompt)
            ai_mappings = [mapping.dict() for mapping in ai_result.mappings]
            print(f"🎯 AI generated {len(ai_mappings)} intelligent mappings")

        except Exception as e:
            print(f"❌ AI invocation failed: {str(e)}")
            ai_mappings = []

        # 🔄 Convert AI results to StatementMapping objects
        return self.convertAiResultsToMappings(ai_mappings, source_statements, target_statements)

    def convertAiResultsToMappings(self, ai_mappings, source_statements, target_statements):
        """Convert AI results to StatementMapping objects with 1:1 validation and tie-breaking by closest line."""
        mappings = []
        mapped_sources = set()
        mapped_targets = set()

        # Pure LLM sorting: prioritize exact matches, then line distance (no confidence)
        def sort_key(x):
            source_line = x.get('source_line', 0)
            target_line = x.get('target_line', 0)

            # Get actual statements for comparison
            source_stmt = ""
            target_stmt = ""
            if 1 <= source_line <= len(source_statements):
                source_stmt = source_statements[source_line - 1].strip()
            if 1 <= target_line <= len(target_statements):
                target_stmt = target_statements[target_line - 1].strip()

            # Check for exact statement match (highest priority)
            exact_match = 1 if source_stmt.lower() == target_stmt.lower() else 0

            # Calculate line distance (handle None values)
            if source_line is not None and target_line is not None:
                line_distance = abs(source_line - target_line)
            else:
                line_distance = 999  # Large distance for None values

            # Sort by: exact_match (desc), line_distance (asc) - NO confidence needed
            return (exact_match, -line_distance)

        ai_mappings_sorted = sorted(ai_mappings, key=sort_key, reverse=True)

        print("🔍 Pure LLM sorting: exact matches first, then line distance (no confidence scores)...")

        print("🔍 Validating AI mappings with tie-breaking by closest lines...")

        # Process AI mappings with pure LLM validation (no confidence scores)
        for i, ai_mapping in enumerate(ai_mappings_sorted):
            try:
                source_line = ai_mapping.get('source_line', 0)
                target_line = ai_mapping.get('target_line', 0)
                status = ai_mapping.get('status', 'Unknown')
                reason = ai_mapping.get('reason', 'LLM-generated')

                # Get actual statements for debugging
                source_stmt = ""
                target_stmt = ""
                if 1 <= source_line <= len(source_statements):
                    source_stmt = source_statements[source_line - 1].strip()
                if 1 <= target_line <= len(target_statements):
                    target_stmt = target_statements[target_line - 1].strip()

                exact_match = source_stmt.lower() == target_stmt.lower()

                # Pure LLM validation - if LLM says "Matched", accept it (no confidence threshold)
                if status == "Matched":
                    if (1 <= source_line <= len(source_statements) and
                        1 <= target_line <= len(target_statements)):

                        # Check for duplicates with enhanced debugging
                        if source_line in mapped_sources:
                            print(f"❌ DUPLICATE SOURCE: S{source_line} already mapped, skipping {'[EXACT MATCH]' if exact_match else ''}")
                            continue

                        if target_line in mapped_targets:
                            print(f"❌ DUPLICATE TARGET: T{target_line} already mapped, skipping {'[EXACT MATCH]' if exact_match else ''}")
                            continue

                        # Create valid mapping based on pure LLM decision
                        mapping = StatementMapping(
                            source_statement=source_statements[source_line-1],
                            source_line_number=source_line,
                            target_statement=target_statements[target_line-1],
                            target_line_number=target_line,
                            status="Matched"
                        )
                        mappings.append(mapping)
                        mapped_sources.add(source_line)
                        mapped_targets.add(target_line)
                        line_distance = abs(source_line - target_line)
                        exact_flag = "[EXACT]" if exact_match else ""
                        print(f"✅ LLM MATCH: S{source_line}→T{target_line} (dist:{line_distance}) {exact_flag} - {reason}")

            except Exception as e:
                print(f"⚠️ Skipping invalid AI mapping: {e}")

        # Add unmapped source statements
        for i, stmt in enumerate(source_statements):
            if (i + 1) not in mapped_sources:
                mapping = StatementMapping(
                    source_statement=stmt,
                    source_line_number=i + 1,
                    target_statement="",
                    target_line_number=0,
                    status="Source Only"
                )
                mappings.append(mapping)

        # Add unmapped target statements
        for i, stmt in enumerate(target_statements):
            if (i + 1) not in mapped_targets:
                mapping = StatementMapping(
                    source_statement="",
                    source_line_number=0,
                    target_statement=stmt,
                    target_line_number=i + 1,
                    status="Target Only"
                )
                mappings.append(mapping)

        matched = len([m for m in mappings if m.status == "Matched"])
        source_only = len([m for m in mappings if m.status == "Source Only"])
        target_only = len([m for m in mappings if m.status == "Target Only"])

        print(f"📈 VALIDATED Results: {matched} matched, {source_only} source-only, {target_only} target-only")
        print(f"🔒 Duplicate prevention: Rejected {len(ai_mappings_sorted) - matched} duplicate mappings")
        return mappings

    def saveMappingsToFile(self, mappings):
        """Save initial mappings to single Excel file after mapping step."""
        try:
            # Convert to DataFrame
            mapping_data = []
            for mapping in mappings:
                mapping_data.append({
                    "source_statement": mapping.source_statement,
                    "source_line_number": mapping.source_line_number,
                    "target_statement": mapping.target_statement,
                    "target_line_number": mapping.target_line_number,
                    "status": mapping.status
                })

            df = pd.DataFrame(mapping_data)

            # Create mappings directory
            os.makedirs("mappings", exist_ok=True)

            # Save to Excel ONLY (FIRST FILE - after mapping)
            excel_path = os.path.join("mappings", "01_initial_mappings.xlsx")
            df.to_excel(excel_path, index=False, sheet_name="Initial Mappings")

            print(f"💾 Saved initial mappings (after mapping step) to:")
            print(f"   📊 Excel: {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save initial mappings: {e}")

    def validateMappings(self, state: WorkflowState) -> Dict[str, Any]:
        """🔍 Validate mappings and identify wrong ones for correction."""
        try:
            print("🔍 Starting mapping validation...")

            # Get current mappings (use validated_mappings if available, otherwise mappings)
            mappings = state.validated_mappings or state.mappings or []
            source_statements = state.source_statements or []
            target_statements = state.target_statements or []

            if not mappings:
                print("⚠️ No mappings to validate")
                return {
                    "validated_mappings": [],
                    "validation_passed": False,
                    "validation_issues": ["No mappings to validate"],
                    "wrong_mappings": []
                }

            print(f"🔍 Validating {len(mappings)} mappings...")

            # First check for duplicates
            duplicate_issues = self.checkForDuplicates(mappings)

            # Then identify wrong mappings
            wrong_mappings = self.identifyWrongMappingsDirectly(mappings, source_statements, target_statements)

            # Add duplicate mappings to wrong mappings
            wrong_mappings.extend(duplicate_issues)

            if len(wrong_mappings) == 0:
                print("✅ Mapping validation PASSED - all mappings are correct")
                # Save validated mappings to separate Excel file
                self.saveValidatedMappingsToFile(mappings, "PASSED")
                return {
                    "validated_mappings": mappings,
                    "validation_passed": True,
                    "validation_issues": [],
                    "wrong_mappings": []
                }
            else:
                print(f"❌ Mapping validation FAILED - {len(wrong_mappings)} wrong mappings found")
                for wrong_mapping in wrong_mappings:
                    print(f"   ⚠️ Wrong: S{wrong_mapping.source_line_number}→T{wrong_mapping.target_line_number}")

                # Return validation failure with specific wrong mappings
                return {
                    "validated_mappings": mappings,
                    "validation_passed": False,
                    "validation_issues": [f"Found {len(wrong_mappings)} wrong mappings"],
                    "wrong_mappings": wrong_mappings
                }

        except Exception as e:
            print(f"❌ Error in mapping validation: {str(e)}")
            return {
                "validated_mappings": state.mappings or [],
                "validation_passed": False,
                "validation_issues": [f"Validation error: {str(e)}"],
                "wrong_mappings": []
            }

    def checkForDuplicates(self, mappings):
        """🔍 Check for duplicate source or target line mappings."""
        print("🔍 Checking for duplicate mappings...")

        duplicate_mappings = []
        matched_mappings = [m for m in mappings if m.status == "Matched"]

        # Track used source and target lines
        used_sources = {}
        used_targets = {}

        for mapping in matched_mappings:
            source_line = mapping.source_line_number
            target_line = mapping.target_line_number

            # Check for duplicate source lines
            if source_line in used_sources:
                print(f"❌ DUPLICATE SOURCE: S{source_line} mapped to both T{used_sources[source_line]} and T{target_line}")
                duplicate_mappings.append(mapping)
            else:
                used_sources[source_line] = target_line

            # Check for duplicate target lines
            if target_line in used_targets:
                print(f"❌ DUPLICATE TARGET: T{target_line} mapped from both S{used_targets[target_line]} and S{source_line}")
                duplicate_mappings.append(mapping)
            else:
                used_targets[target_line] = source_line

        if len(duplicate_mappings) == 0:
            print("✅ No duplicate mappings found")
        else:
            print(f"❌ Found {len(duplicate_mappings)} duplicate mappings")

        return duplicate_mappings

    def identifyWrongMappingsDirectly(self, mappings, source_statements, target_statements):
        """🎯 Use AI to dynamically identify wrong mappings without hardcoded rules."""
        print("🧠 Using AI to validate mappings dynamically...")

        # Create validation prompt for AI
        mapping_list = []
        for mapping in mappings:
            if mapping.status == "Matched":
                mapping_list.append(f"S{mapping.source_line_number}→T{mapping.target_line_number}: '{mapping.source_statement.strip()}' → '{mapping.target_statement.strip()}'")

        mappings_text = "\n".join(mapping_list[:20])  # Limit to first 20 for prompt size

        prompt = f"""You are an expert Oracle to PostgreSQL migration validator. Analyze these mappings and identify which ones are FUNCTIONALLY WRONG.

MAPPINGS TO VALIDATE:
{mappings_text}

ORACLE TO POSTGRESQL VALIDATION CRITERIA:
1. Do the source and target statements serve the same BUSINESS PURPOSE?
2. Are they functionally equivalent in Oracle to PostgreSQL conversion?
3. Do they represent the same logical operation?
4. Would a developer expect these statements to correspond in migration?

IGNORE THESE SYNTAX DIFFERENCES (NOT WRONG):
- Case sensitivity: true vs TRUE, varchar vs VARCHAR, boolean vs BOOLEAN
- Data type syntax: VARCHAR2 vs varchar, NUMBER vs numeric, INT vs int
- Keyword variations: BEGIN vs begin, END vs end
- Quote differences: single vs double quotes for equivalent values
- Whitespace and formatting differences

IDENTIFY ONLY FUNCTIONALLY WRONG MAPPINGS:
- Statements that do completely different business logic
- Mismatched functionality (e.g., variable declaration mapped to loop)
- Statements that don't logically correspond in migration context
- Different business operations mapped together

OUTPUT FORMAT - List only the FUNCTIONALLY WRONG mappings:
{{
  "wrong_mappings": [
    {{
      "source_line": 15,
      "target_line": 17,
      "reason": "Source is variable assignment, target is loop statement - completely different business logic"
    }}
  ]
}}

IMPORTANT: Only include mappings that are FUNCTIONALLY wrong. Ignore syntax/case differences."""

        try:
            # Use structured output for reliable parsing
            from state import WrongMappingOutput

            structured_llm = self.llm.client.with_structured_output(WrongMappingOutput)
            ai_result = structured_llm.invoke(prompt)
            ai_wrong_mappings = ai_result.wrong_mappings
            print(f"🎯 AI identified {len(ai_wrong_mappings)} wrong mappings")

            # Convert AI results to actual mapping objects
            wrong_mappings = []
            for ai_wrong in ai_wrong_mappings:
                source_line = ai_wrong.source_line
                target_line = ai_wrong.target_line
                reason = ai_wrong.reason

                # Find the actual mapping object
                for mapping in mappings:
                    if (mapping.source_line_number == source_line and
                        mapping.target_line_number == target_line and
                        mapping.status == "Matched"):
                        wrong_mappings.append(mapping)
                        print(f"   ⚠️ Wrong: S{source_line}→T{target_line} - {reason}")
                        break

            return wrong_mappings

        except Exception as e:
            print(f"❌ AI validation failed: {str(e)}")
            print("🔧 Assuming no wrong mappings due to error")
            return []

    def fixMalformedJson(self, json_text):
        """Attempt to fix common JSON formatting issues."""
        try:
            import re

            # Common fixes for malformed JSON
            fixed = json_text

            # Fix missing commas
            fixed = re.sub(r'"\s*\n\s*"', '",\n    "', fixed)

            # Fix trailing commas
            fixed = re.sub(r',\s*}', '}', fixed)
            fixed = re.sub(r',\s*]', ']', fixed)

            # Fix unescaped quotes in strings
            fixed = re.sub(r'(?<!\\)"(?![,}\]:])(?![^"]*"[,}\]:])', '\\"', fixed)

            return fixed
        except:
            return None

    def analyzeMappingQuality(self, mappings, source_statements, target_statements):
        """🔍 Pure AI-based mapping quality analysis - completely dynamic."""
        print("🔍 Using AI for dynamic mapping quality analysis...")

        # Use AI validation instead of hardcoded rules
        wrong_mappings = self.identifyWrongMappingsDirectly(mappings, source_statements, target_statements)

        validation_passed = len(wrong_mappings) == 0
        issues = [f"Wrong mapping: S{m.source_line_number}→T{m.target_line_number}" for m in wrong_mappings]

        print(f"📊 AI validation analysis: {len(issues)} issues found")

        return {
            "validation_passed": validation_passed,
            "issues": issues,
            "total_mappings": len(mappings),
            "matched_mappings": len([m for m in mappings if m.status == "Matched"])
        }

    # Pure AI validation and correction - no hardcoded rules

    def correctWrongMappingsWithAI(self, mappings, source_statements, target_statements, issues):
        """Correct wrong mappings using AI with specific issue feedback."""
        print("🔧 Correcting wrong mappings with AI...")

        # Identify wrong mappings from issues
        wrong_mappings = self.identifyWrongMappings(mappings, issues)

        if not wrong_mappings:
            print("✅ No wrong mappings to correct")
            return mappings

        print(f"🎯 Found {len(wrong_mappings)} wrong mappings to correct")

        # Get correct mappings (keep the good ones)
        correct_mappings = [m for m in mappings if m not in wrong_mappings]

        # Get unmapped statements for re-mapping
        mapped_source_lines = {m.source_line_number for m in correct_mappings if m.status == "Matched"}
        mapped_target_lines = {m.target_line_number for m in correct_mappings if m.status == "Matched"}

        unmapped_source_statements = []
        unmapped_target_statements = []

        for i, stmt in enumerate(source_statements):
            if (i + 1) not in mapped_source_lines:
                unmapped_source_statements.append((i + 1, stmt))

        for i, stmt in enumerate(target_statements):
            if (i + 1) not in mapped_target_lines:
                unmapped_target_statements.append((i + 1, stmt))

        # Re-map the unmapped statements with AI correction
        if unmapped_source_statements and unmapped_target_statements:
            corrected_mappings = self.aiCorrectMappings(unmapped_source_statements, unmapped_target_statements, issues)

            # Combine correct mappings with corrected mappings
            all_mappings = correct_mappings + corrected_mappings
        else:
            all_mappings = correct_mappings

        # Add any remaining unmapped statements
        final_mappings = self.addUnmappedStatements(all_mappings, source_statements, target_statements)

        print(f"✅ Mapping correction complete: {len(final_mappings)} total mappings")
        return final_mappings

    def identifyWrongMappings(self, mappings, issues):
        """Identify which mappings are wrong based on validation issues."""
        wrong_mappings = []

        print(f"🔍 Analyzing {len(issues)} validation issues to identify wrong mappings...")

        for issue in issues:
            print(f"   📝 Issue: {issue}")

            # Extract line numbers from different issue patterns
            if "S" in issue and "T" in issue and ("→" in issue or "(" in issue):
                try:
                    # Pattern 1: "Wrong mapping: S{source_line}→T{target_line}"
                    if "Wrong mapping:" in issue and "→" in issue:
                        parts = issue.split("→")
                        if len(parts) == 2:
                            source_part = parts[0].strip()
                            target_part = parts[1].split()[0].strip()

                            if "S" in source_part and "T" in target_part:
                                source_line = int(source_part.split("S")[-1])
                                target_line = int(target_part.split("T")[-1])

                                # Find and mark this mapping as wrong
                                for mapping in mappings:
                                    if (mapping.source_line_number == source_line and
                                        mapping.target_line_number == target_line and
                                        mapping.status == "Matched"):
                                        wrong_mappings.append(mapping)
                                        print(f"   ✅ Found wrong mapping: S{source_line}→T{target_line}")
                                        break

                    # Pattern 2: "Type mismatch: S{source_line}({type})→T{target_line}({type})"
                    elif "Type mismatch:" in issue and "→" in issue:
                        # Extract S{number} and T{number} from the issue
                        import re
                        s_match = re.search(r'S(\d+)', issue)
                        t_match = re.search(r'T(\d+)', issue)

                        if s_match and t_match:
                            source_line = int(s_match.group(1))
                            target_line = int(t_match.group(1))

                            # Find and mark this mapping as wrong
                            for mapping in mappings:
                                if (mapping.source_line_number == source_line and
                                    mapping.target_line_number == target_line and
                                    mapping.status == "Matched"):
                                    wrong_mappings.append(mapping)
                                    print(f"   ✅ Found type mismatch mapping: S{source_line}→T{target_line}")
                                    break

                except (ValueError, IndexError, AttributeError) as e:
                    print(f"   ⚠️ Could not parse issue: {e}")
                    continue

        print(f"🎯 Identified {len(wrong_mappings)} wrong mappings from {len(issues)} issues")
        return wrong_mappings

    def aiCorrectMappings(self, unmapped_source_statements, unmapped_target_statements, issues):
        """Use AI to correctly map the unmapped statements with enhanced correction logic."""
        print("🧠 Using AI to correct mappings with enhanced validation...")

        source_list = "\n".join([f"{line_num:3d}. {stmt.strip()}" for line_num, stmt in unmapped_source_statements])
        target_list = "\n".join([f"{line_num:3d}. {stmt.strip()}" for line_num, stmt in unmapped_target_statements])

        prompt = f"""You are an expert Oracle to PostgreSQL migration specialist. You must create PERFECT mappings between Oracle source and PostgreSQL target statements using pure AI intelligence.

CRITICAL CONTEXT: The target code is a converted PostgreSQL version of the Oracle source code. You need to identify which PostgreSQL target statements correspond to which Oracle source statements.

SOURCE STATEMENTS TO MAP:
{source_list}

TARGET STATEMENTS TO MAP:
{target_list}

PURE AI MAPPING RULES:
1. ANALYZE statement PURPOSE and FUNCTIONALITY using AI intelligence
2. Map based on WHAT THE STATEMENT DOES, not syntax patterns
3. Use AI to understand conversion relationships
4. ONLY map functionally equivalent statements
5. Let AI determine statement compatibility

AI INTELLIGENCE APPROACH:
- Use deep understanding of code conversion patterns
- Analyze functional equivalence dynamically
- Consider business logic preservation
- Understand syntax transformations in conversions
- Apply intelligent pattern recognition

ORACLE TO POSTGRESQL MAPPING CRITERIA:
- Use DEEP LLM UNDERSTANDING to determine functional equivalence
- Apply SEMANTIC ANALYSIS to identify corresponding statements
- Use CONTEXTUAL INTELLIGENCE for Oracle to PostgreSQL conversion patterns
- IGNORE case sensitivity differences (true vs TRUE, varchar vs VARCHAR)
- IGNORE syntax variations that preserve functionality
- Focus on business logic equivalence, not syntactic exactness
- If LLM determines statements are functionally equivalent → MAP THEM
- If LLM determines statements serve different purposes → DON'T MAP

OUTPUT JSON FORMAT:
{{
  "mappings": [
    {{
      "source_line": 1,
      "target_line": 1,
      "reason": "LLM identified functionally equivalent statements through semantic analysis",
      "status": "Matched"
    }}
  ]
}}

LLM INTELLIGENCE FOCUS:
- Use pure LLM reasoning for functional equivalence
- Apply deep language understanding for statement correspondence
- NO confidence scores - LLM makes the mapping decision
- Create INTELLIGENT mappings using pure LLM analysis"""

        try:
            # Use structured output for reliable parsing
            from state import MappingOutput

            structured_llm = self.llm.client.with_structured_output(MappingOutput)
            ai_result = structured_llm.invoke(prompt)
            ai_mappings = [mapping.dict() for mapping in ai_result.mappings]
            print(f"🎯 AI generated {len(ai_mappings)} corrected mappings")

        except Exception as e:
            print(f"❌ AI correction failed: {str(e)}")
            ai_mappings = []

        # Convert to StatementMapping objects with STRICT duplicate prevention
        corrected_mappings = []
        used_sources = set()
        used_targets = set()

        for ai_mapping in ai_mappings:
            try:
                source_line = ai_mapping.get('source_line', 0)
                target_line = ai_mapping.get('target_line', 0)
                status = ai_mapping.get('status', 'Unknown')

                # Pure LLM validation - if LLM says "Matched", accept it (no confidence threshold)
                if status == "Matched":
                    # STRICT duplicate check for correction
                    if source_line in used_sources:
                        print(f"❌ CORRECTION DUPLICATE: Source S{source_line} already used in correction")
                        continue

                    if target_line in used_targets:
                        print(f"❌ CORRECTION DUPLICATE: Target T{target_line} already used in correction")
                        continue

                    # Find the actual statements
                    source_stmt = ""
                    target_stmt = ""

                    for line_num, stmt in unmapped_source_statements:
                        if line_num == source_line:
                            source_stmt = stmt
                            break

                    for line_num, stmt in unmapped_target_statements:
                        if line_num == target_line:
                            target_stmt = stmt
                            break

                    if source_stmt and target_stmt:
                        mapping = StatementMapping(
                            source_statement=source_stmt,
                            source_line_number=source_line,
                            target_statement=target_stmt,
                            target_line_number=target_line,
                            status="Matched"
                        )
                        corrected_mappings.append(mapping)
                        used_sources.add(source_line)
                        used_targets.add(target_line)
                        print(f"✅ Corrected mapping: S{source_line}→T{target_line}")

            except Exception as e:
                print(f"⚠️ Skipping invalid corrected mapping: {e}")

        return corrected_mappings

    def removeDuplicates(self, state: WorkflowState) -> Dict[str, Any]:
        """🧹 Remove any remaining duplicate mappings after validation passes."""
        try:
            print("🧹 Starting duplicate removal process...")

            mappings = state.validated_mappings or []
            if not mappings:
                print("⚠️ No mappings to process for duplicate removal")
                return {
                    "cleaned_mappings": [],
                    "duplicates_removed": 0
                }

            print(f"🔍 Processing {len(mappings)} mappings for duplicate removal...")

            # Track unique mappings
            unique_mappings = []
            seen_sources = set()
            seen_targets = set()
            duplicates_removed = 0

            # Process matched mappings first to ensure 1:1 mapping
            matched_mappings = [m for m in mappings if m.status == "Matched"]
            other_mappings = [m for m in mappings if m.status != "Matched"]

            # Sort matched mappings by confidence (if available) or line distance
            matched_mappings.sort(key=lambda m: abs(m.source_line_number - m.target_line_number))

            for mapping in matched_mappings:
                source_line = mapping.source_line_number
                target_line = mapping.target_line_number

                # Check for duplicates
                if source_line in seen_sources or target_line in seen_targets:
                    print(f"🗑️ Removing duplicate: S{source_line}→T{target_line}")
                    duplicates_removed += 1
                    continue

                # Add unique mapping
                unique_mappings.append(mapping)
                seen_sources.add(source_line)
                seen_targets.add(target_line)

            # Add non-matched mappings (Source Only, Target Only)
            for mapping in other_mappings:
                if mapping.status == "Source Only" and mapping.source_line_number not in seen_sources:
                    unique_mappings.append(mapping)
                    seen_sources.add(mapping.source_line_number)
                elif mapping.status == "Target Only" and mapping.target_line_number not in seen_targets:
                    unique_mappings.append(mapping)
                    seen_targets.add(mapping.target_line_number)
                else:
                    duplicates_removed += 1

            print(f"✅ Duplicate removal complete: {duplicates_removed} duplicates removed")
            print(f"📊 Final count: {len(unique_mappings)} unique mappings")

            return {
                "cleaned_mappings": unique_mappings,
                "duplicates_removed": duplicates_removed
            }

        except Exception as e:
            print(f"❌ Error in duplicate removal: {str(e)}")
            return {
                "cleaned_mappings": state.validated_mappings or [],
                "duplicates_removed": 0
            }

    def sortMappings(self, state: WorkflowState) -> Dict[str, Any]:
        """📋 Sort mappings by line numbers in proper format."""
        try:
            print("📋 Starting mapping sorting process...")

            mappings = state.cleaned_mappings or state.validated_mappings or []
            source_statements = state.source_statements or []
            target_statements = state.target_statements or []

            if not mappings:
                print("⚠️ No mappings to sort")
                return {
                    "sorted_mappings": [],
                    "final_excel_saved": False
                }

            print(f"📊 Sorting {len(mappings)} mappings by line numbers...")

            # Create a comprehensive mapping structure
            sorted_mappings = []

            # Get all source and target line numbers
            all_source_lines = set(range(1, len(source_statements) + 1))
            all_target_lines = set(range(1, len(target_statements) + 1))

            # Track which lines are matched
            matched_sources = {}  # source_line -> target_line
            matched_targets = {}  # target_line -> source_line

            # First pass: collect all matched mappings
            for mapping in mappings:
                if mapping.status == "Matched":
                    matched_sources[mapping.source_line_number] = mapping.target_line_number
                    matched_targets[mapping.target_line_number] = mapping.source_line_number

            # Create sorted output in the requested format
            max_lines = max(len(source_statements), len(target_statements))

            for line_num in range(1, max_lines + 1):
                # Check if this source line is matched
                if line_num <= len(source_statements):
                    if line_num in matched_sources:
                        target_line = matched_sources[line_num]
                        # Matched pair
                        mapping = StatementMapping(
                            source_statement=source_statements[line_num - 1],
                            source_line_number=line_num,
                            target_statement=target_statements[target_line - 1],
                            target_line_number=target_line,
                            status="Matched"
                        )
                        sorted_mappings.append(mapping)
                    else:
                        # Source only
                        mapping = StatementMapping(
                            source_statement=source_statements[line_num - 1],
                            source_line_number=line_num,
                            target_statement="",
                            target_line_number=0,
                            status="Source Only"
                        )
                        sorted_mappings.append(mapping)

                # Check if this target line is unmatched
                if line_num <= len(target_statements):
                    if line_num not in matched_targets:
                        # Target only
                        mapping = StatementMapping(
                            source_statement="",
                            source_line_number=0,
                            target_statement=target_statements[line_num - 1],
                            target_line_number=line_num,
                            status="Target Only"
                        )
                        sorted_mappings.append(mapping)

            # Save final sorted mappings to Excel
            self.saveFinalSortedMappings(sorted_mappings)

            print(f"✅ Sorting complete: {len(sorted_mappings)} mappings organized by line numbers")

            return {
                "sorted_mappings": sorted_mappings,
                "final_excel_saved": True
            }

        except Exception as e:
            print(f"❌ Error in mapping sorting: {str(e)}")
            return {
                "sorted_mappings": state.cleaned_mappings or state.validated_mappings or [],
                "final_excel_saved": False
            }

    def saveFinalSortedMappings(self, mappings):
        """Save final sorted mappings to Excel file."""
        try:
            # Convert to DataFrame
            mapping_data = []
            for mapping in mappings:
                mapping_data.append({
                    "source_statement": mapping.source_statement,
                    "source_line_number": mapping.source_line_number,
                    "target_statement": mapping.target_statement,
                    "target_line_number": mapping.target_line_number,
                    "status": mapping.status
                })

            df = pd.DataFrame(mapping_data)

            # Create mappings directory
            os.makedirs("mappings", exist_ok=True)

            # Save final sorted mappings
            excel_path = os.path.join("mappings", "03_final_sorted_mappings.xlsx")
            df.to_excel(excel_path, index=False, sheet_name="Final Sorted Mappings")

            print(f"💾 Saved final sorted mappings to:")
            print(f"   📊 Excel: {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save final sorted mappings: {e}")

    def addUnmappedStatements(self, mappings, source_statements, target_statements):
        """Add any remaining unmapped statements as source-only or target-only."""
        mapped_sources = {m.source_line_number for m in mappings if m.status == "Matched"}
        mapped_targets = {m.target_line_number for m in mappings if m.status == "Matched"}

        final_mappings = list(mappings)

        # Add unmapped source statements
        for i, stmt in enumerate(source_statements):
            if (i + 1) not in mapped_sources:
                mapping = StatementMapping(
                    source_statement=stmt,
                    source_line_number=i + 1,
                    target_statement="",
                    target_line_number=0,
                    status="Source Only"
                )
                final_mappings.append(mapping)

        # Add unmapped target statements
        for i, stmt in enumerate(target_statements):
            if (i + 1) not in mapped_targets:
                mapping = StatementMapping(
                    source_statement="",
                    source_line_number=0,
                    target_statement=stmt,
                    target_line_number=i + 1,
                    status="Target Only"
                )
                final_mappings.append(mapping)

        return final_mappings

    def saveValidatedMappingsToFile(self, mappings, validation_status):
        """Save validated mappings to single Excel file (SECOND FILE - after validation)."""
        try:
            # Convert to DataFrame
            mapping_data = []
            for mapping in mappings:
                mapping_data.append({
                    "source_statement": mapping.source_statement,
                    "source_line_number": mapping.source_line_number,
                    "target_statement": mapping.target_statement,
                    "target_line_number": mapping.target_line_number,
                    "status": mapping.status
                })

            df = pd.DataFrame(mapping_data)

            # Create mappings directory
            os.makedirs("mappings", exist_ok=True)

            # Save to Excel ONLY (SECOND FILE - after validation)
            if validation_status == "PASSED":
                excel_path = os.path.join("mappings", "02_validated_mappings.xlsx")
                sheet_name = "Validated Mappings (Final)"
            else:  # CORRECTED - but don't save during correction, only at final validation
                return  # Don't save during correction attempts

            df.to_excel(excel_path, index=False, sheet_name=sheet_name)

            print(f"💾 Saved final validated mappings to:")
            print(f"   📊 Excel: {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save validated mappings: {e}")

    def correctMappings(self, state: WorkflowState) -> Dict[str, Any]:
        """🔧 Correct wrong mappings identified by validation using AI."""
        try:
            print("🔧 Starting mapping correction...")

            # Get current mappings and wrong mappings
            mappings = state.validated_mappings or state.mappings or []
            source_statements = state.source_statements or []
            target_statements = state.target_statements or []
            wrong_mappings = getattr(state, 'wrong_mappings', [])

            if not wrong_mappings:
                print("✅ No wrong mappings to correct")
                return {
                    "mappings": mappings,
                    "validated_mappings": mappings,
                    "validation_passed": True,
                    "validation_issues": [],
                    "wrong_mappings": []
                }

            print(f"🎯 Correcting {len(wrong_mappings)} wrong mappings with AI...")

            # Remove wrong mappings and get unmapped statements
            correct_mappings = [m for m in mappings if m not in wrong_mappings]

            # Get statements that need to be re-mapped
            mapped_source_lines = {m.source_line_number for m in correct_mappings if m.status == "Matched"}
            mapped_target_lines = {m.target_line_number for m in correct_mappings if m.status == "Matched"}

            unmapped_source_statements = []
            unmapped_target_statements = []

            # Add wrong mapping source statements to unmapped
            for wrong_mapping in wrong_mappings:
                unmapped_source_statements.append((wrong_mapping.source_line_number, wrong_mapping.source_statement))
                unmapped_target_statements.append((wrong_mapping.target_line_number, wrong_mapping.target_statement))

            # Add any other unmapped statements
            for i, stmt in enumerate(source_statements):
                if (i + 1) not in mapped_source_lines and (i + 1) not in [s[0] for s in unmapped_source_statements]:
                    unmapped_source_statements.append((i + 1, stmt))

            for i, stmt in enumerate(target_statements):
                if (i + 1) not in mapped_target_lines and (i + 1) not in [t[0] for t in unmapped_target_statements]:
                    unmapped_target_statements.append((i + 1, stmt))

            # Use AI to correct the mappings
            if unmapped_source_statements and unmapped_target_statements:
                print(f"🧠 Using AI to re-map {len(unmapped_source_statements)} source and {len(unmapped_target_statements)} target statements")
                corrected_mappings = self.aiCorrectMappings(unmapped_source_statements, unmapped_target_statements, [])

                # Combine correct mappings with corrected mappings
                all_mappings = correct_mappings + corrected_mappings
            else:
                all_mappings = correct_mappings

            # Add any remaining unmapped statements
            final_mappings = self.addUnmappedStatements(all_mappings, source_statements, target_statements)

            print(f"✅ Mapping correction complete: {len(final_mappings)} total mappings")

            # Increment correction attempts
            correction_attempts = getattr(state, 'correction_attempts', 0) + 1

            return {
                "mappings": final_mappings,
                "validated_mappings": final_mappings,
                "validation_passed": None,  # Will be determined by next validation
                "validation_issues": [],
                "wrong_mappings": [],
                "correction_attempts": correction_attempts
            }

        except Exception as e:
            print(f"❌ Error in mapping correction: {str(e)}")
            return {
                "mappings": state.mappings or [],
                "validated_mappings": state.validated_mappings or [],
                "validation_passed": False,
                "validation_issues": [f"Correction error: {str(e)}"],
                "wrong_mappings": []
            }




