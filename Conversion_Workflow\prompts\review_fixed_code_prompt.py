"""
Prompts for reviewing fixed code in code migrations.
"""

def review_fixed_code_prompt(source_code: str, target_code: str, fixed_code: str, deployment_error_message: str) -> str:
    """
    Creates a prompt for reviewing the fixed code to ensure it properly addresses the error
    without introducing new issues or making unnecessary changes.

    Args:
        source_code: The source code
        target_code: The original target code with errors
        fixed_code: The fixed target code
        deployment_error_message: The error message from deployment

    Returns:
        A formatted prompt string for the LLM
    """
    return f"""
            You are an expert code migration reviewer. Your task is to review the fixed code and ensure it properly addresses the error without introducing new issues.

            Source code:
            ```
            {source_code}
            ```

            Original Target code with errors:
            ```
            {target_code}
            ```

            Fixed Target code:
            ```
            {fixed_code}
            ```

            Error message that needed to be fixed:
            ```
            {deployment_error_message}
            ```

            CRITICAL REVIEW INSTRUCTIONS:
            FIRST STEP - COMPREHENSIVE CODE COMPARISON:
            1. PERFORM A DETAILED LINE-BY-LINE COMPARISON of the original target code and fixed code
            2. CREATE A LIST of all lines that were added, removed, or modified
            3. IDENTIFY ANY SECTIONS that were completely removed or significantly altered
            4. COUNT the number of code blocks, control structures, and error handlers in both versions
            5. VERIFY that all code blocks from the original are present in the fixed code
            6. CHECK if any unnecessary code was added or if any necessary code was removed

            SECOND STEP - ERROR FIX ASSESSMENT:
            7. Verify that the fixed code properly addresses the specific error mentioned in the error message
            8. Check if the fix is minimal and only changes what's necessary to fix the error
            9. Ensure that the fix is consistent across all similar instances in the code

            THIRD STEP - QUALITY VERIFICATION:
            10. Verify that no unnecessary changes were made to functions, syntax, or structure
            11. CAREFULLY CHECK that no code was removed or commented out unnecessarily
            12. Ensure that all comments from the original code are preserved
            13. Verify that no duplicate lines or redundant code was added
            14. Check that the fix maintains the original structure and intent of the code
            15. Pay special attention to error handling blocks - ensure they are properly structured
            16. EXPLICITLY CHECK for duplicate error handlers in the original code and verify they are properly handled in the fixed code
            17. Count the number of WHEN clauses in both original and fixed code to ensure they match
            18. If the original code has multiple WHEN OTHERS clauses, verify they are ALL properly handled in the fixed code

            Provide your review in the following format:
            - is_properly_fixed: true/false (whether the error is properly fixed)
            - review_comments: Detailed comments about the quality of the fix
            - issues_found: List of any issues found in the fixed code
            - recommendations: Recommendations for improving the fix if needed
            """
