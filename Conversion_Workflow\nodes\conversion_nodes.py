from state import WorkflowState, CodeOutput, IdentifierPair, FixedCodeOutput, ReviewFixedCodeOutput
# from state.state import MappingStatement
from typing import Dict, Any, List
import re
from prompts.error_analysis_prompt import create_error_analysis_prompt
from prompts.identify_error_fix_prompt import identify_error_fix_prompt
from prompts.review_fixed_code_prompt import review_fixed_code_prompt
# from embeddings.embedding_mapper import EmbeddingMapper


class Conversion_Flow_Nodes:
    def __init__(self, llm):
        self.llm = llm

    def error_analysis_identify_pairs(self, state: WorkflowState):
        """Identify the specific problematic SQL statement pair based on deployment error message.

        This node analyzes a PostgreSQL deployment error message and identifies the exact
        source (Oracle) and target (PostgreSQL) SQL statements that are causing the error.
        It uses the existing mapping statements to find the problematic pair without
        analyzing the full source and target code.

        Args:
            state: Current workflow state containing mappings_statements and deployment_error_message

        Returns:
            Dictionary containing the identified source_statement and target_statement that match
            the error described in the deployment_error_message
        """
        try:
            deployment_error_message = state.deployment_error_message
            mappings_statements = state.mappings_statements

            # Use the imported prompt function with mappings instead of full code
            prompt = create_error_analysis_prompt(
                deployment_error_message=deployment_error_message,
                mappings_statements=mappings_statements
            )

            structured_llm = self.llm.client.with_structured_output(IdentifierPair)
            result = structured_llm.invoke(prompt)
            print(result, 'result')
            # Return the identified statements
            return {"source_statement": result.source_statement, "target_statement": result.target_statement}
        except Exception as e:
            print(f"Error in error_analysis_identify_pairs node: {str(e)}")
            return {"error_message": f"Error analyzing deployment error: {str(e)}"}




    def llm_invoke(self, state: WorkflowState) -> Dict[str, Any]:
        """Invoke the LLM to process the input code.

        This node is responsible for invoking the LLM to process the input code.
        It updates the state with the output code.

        Args:
            state: Current workflow state containing input code

        Returns:
            Dictionary containing the output code
        """
        try:
            # Get the input code from the state
            input_code = state.input_code

            # Create a prompt string for the LLM
            prompt = f"You are an expert Python developer specializing in code documentation. Add clear, concise docstrings to the provided code following PEP 257 standards.\n\nPlease add proper docstrings to this Python code and return the complete documented code:\n\n{input_code}"

            structured_llm = self.llm.client.with_structured_output(CodeOutput)
            result = structured_llm.invoke(prompt)

            # Return the updated state
            return {"output_code": result.output_code}
        except Exception as e:
            print(f"Error in llm_invoke node: {str(e)}")
            return {"output_code": f"Error processing code: {str(e)}"}

    # def split_sql_statements(self, sql_code: str) -> List[str]:
    #     """
    #     Split SQL code into statements based on specific rules.

    #     Rules:
    #     1. Split at semicolons, but not within SQL comments
    #     2. Don't split at semicolons within single quotes
    #     3. Split at the first occurrence of "as" or "is" keywords
    #     4. Split at semicolons within PostgreSQL dollar-quoted strings to handle multi-statement blocks

    #     Args:
    #         sql_code: The SQL code to split

    #     Returns:
    #         List of SQL statements
    #     """
    #     if not sql_code or not sql_code.strip():
    #         return []

    #     # Normalize line endings
    #     sql_code = sql_code.replace('\r\n', '\n')

    #     # Initialize variables
    #     statements = []
    #     current_statement = ""
    #     in_single_line_comment = False
    #     in_multi_line_comment = False
    #     in_single_quote = False
    #     in_double_quote = False
    #     first_as_is_found = False
    #     last_statement_was_end = False
    #     in_dollar_quote = False
    #     dollar_quote_tag = ""

    #     # Process the SQL code character by character
    #     i = 0
    #     while i < len(sql_code):
    #         char = sql_code[i]
    #         next_char = sql_code[i+1] if i < len(sql_code) - 1 else ""

    #         # Check for comment start/end
    #         if char == '-' and next_char == '-' and not in_multi_line_comment and not in_single_quote and not in_double_quote and not in_dollar_quote:
    #             in_single_line_comment = True
    #             current_statement += char + next_char
    #             i += 2
    #             continue
    #         elif char == '/' and next_char == '*' and not in_single_line_comment and not in_single_quote and not in_double_quote and not in_dollar_quote:
    #             in_multi_line_comment = True
    #             current_statement += char + next_char
    #             i += 2
    #             continue
    #         elif char == '*' and next_char == '/' and in_multi_line_comment:
    #             in_multi_line_comment = False
    #             current_statement += char + next_char
    #             i += 2
    #             continue
    #         elif char == '\n' and in_single_line_comment:
    #             in_single_line_comment = False
    #             current_statement += char
    #             i += 1
    #             continue

    #         # Check for quotes
    #         if char == "'" and not in_single_line_comment and not in_multi_line_comment and not in_dollar_quote:
    #             # Check for escaped single quote
    #             if i > 0 and sql_code[i-1] == '\\':
    #                 current_statement += char
    #             else:
    #                 in_single_quote = not in_single_quote
    #                 current_statement += char
    #             i += 1
    #             continue
    #         elif char == '"' and not in_single_line_comment and not in_multi_line_comment and not in_dollar_quote:
    #             # Check for escaped double quote
    #             if i > 0 and sql_code[i-1] == '\\':
    #                 current_statement += char
    #             else:
    #                 in_double_quote = not in_double_quote
    #                 current_statement += char
    #             i += 1
    #             continue

    #         # Track PostgreSQL dollar-quoted string literals (but still split at semicolons)
    #         if char == '$' and not in_single_line_comment and not in_multi_line_comment and not in_single_quote and not in_double_quote:
    #             if not in_dollar_quote:
    #                 # Look ahead to find the end of the tag
    #                 tag_end_pos = sql_code.find('$', i + 1)
    #                 if tag_end_pos != -1:
    #                     dollar_quote_tag = sql_code[i:tag_end_pos + 1]
    #                     in_dollar_quote = True
    #                     current_statement += dollar_quote_tag
    #                     i += len(dollar_quote_tag)
    #                     continue
    #             else:
    #                 # Check if this is the closing tag
    #                 if i + len(dollar_quote_tag) <= len(sql_code) and sql_code[i:i + len(dollar_quote_tag)] == dollar_quote_tag:
    #                     in_dollar_quote = False
    #                     current_statement += dollar_quote_tag
    #                     i += len(dollar_quote_tag)
    #                     continue

    #         # Check for the first occurrence of "as" or "is" keywords
    #         if not first_as_is_found and not in_single_line_comment and not in_multi_line_comment and not in_single_quote and not in_double_quote and not in_dollar_quote:
    #             # Look ahead for "as" or "is" with word boundaries
    #             remaining = sql_code[i:].lower()
    #             as_is_match = re.search(r'\b(as|is)\b', remaining)

    #             if as_is_match and as_is_match.start() == 0:
    #                 # We found "as" or "is" at the current position
    #                 keyword = as_is_match.group()
    #                 keyword_length = len(keyword)

    #                 # Add the current statement (without as/is) to the list
    #                 if current_statement.strip():
    #                     statements.append(current_statement.strip())

    #                 # Start a new statement with the keyword
    #                 current_statement = keyword
    #                 first_as_is_found = True  # Mark that we've found the first as/is
    #                 i += keyword_length
    #                 continue

    #         # Check for semicolon (only if not in a comment or quote)
    #         # Note: We DO split at semicolons within dollar-quoted strings for PostgreSQL statements
    #         if char == ';' and not in_single_line_comment and not in_multi_line_comment and not in_single_quote and not in_double_quote:
    #             # Add the current statement with the semicolon
    #             current_statement += char

    #             # Check if this is an "END;" statement using regex with case insensitivity
    #             if re.search(r'\bEND\s*;$', current_statement, re.IGNORECASE):
    #                 last_statement_was_end = True
    #                 statements.append(current_statement.strip())
    #                 current_statement = ""
    #             # Check if we need to combine with the previous "END;" statement
    #             elif last_statement_was_end and re.search(r'^\s*\$[^$]*\$', current_statement.strip()):
    #                 # This is a dollar-quoted string after END; - combine with the previous statement
    #                 # It could be like "$BODY$;" or "$BODY$ LANGUAGE plpgsql;" or any other variant
    #                 combined_statement = statements.pop() + " " + current_statement.strip()
    #                 statements.append(combined_statement)
    #                 last_statement_was_end = False
    #                 current_statement = ""
    #             else:
    #                 # Regular statement ending with semicolon
    #                 statements.append(current_statement.strip())
    #                 last_statement_was_end = False
    #                 current_statement = ""

    #             i += 1
    #             continue

    #         # Add the current character to the current statement
    #         current_statement += char
    #         i += 1

    #     # Add any remaining statement
    #     if current_statement.strip():
    #         statements.append(current_statement.strip())

    #     return statements

    # def sql_statements(self, state: WorkflowState) -> Dict[str, Any]:
    #     """Split source and target SQL code into statements.

    #     This node splits the source and target SQL code into statements based on specific rules:
    #     1. Split at semicolons, but not within SQL comments
    #     2. Don't split at semicolons within single quotes
    #     3. Split at the first occurrence of "as" or "is" keywords
    #     4. Split at semicolons within PostgreSQL dollar-quoted strings to handle multi-statement blocks

    #     Args:
    #         state: Current workflow state containing source_code and target_code

    #     Returns:
    #         Dictionary containing the source and target SQL statements
    #     """
    #     try:
    #         # Get the source and target code from the state
    #         source_code = state.source_code
    #         target_code = state.target_code

    #         # Split the source and target code into statements
    #         source_statements = self.split_sql_statements(source_code)
    #         target_statements = self.split_sql_statements(target_code)

    #         print(source_statements, 'source_statements')
    #         print(target_statements, 'target_statements')

    #         # Return the source and target statements
    #         return {
    #             "source_sql_statements": source_statements,
    #             "target_sql_statements": target_statements
    #         }
    #     except Exception as e:
    #         print(f"Error in sql_statements node: {str(e)}")
    #         return {"error_message": f"Error splitting SQL statements: {str(e)}"}

    # def sql_mappings(self, state: WorkflowState) -> Dict[str, Any]:
    #     """Create mappings between source and target SQL statements using embeddings.

    #     This node takes the source and target SQL statements and creates mappings between them
    #     using embeddings and semantic search instead of LLM. Each mapping includes:
    #     - Source statement
    #     - Source statement number (as source_line_number)
    #     - Target statement
    #     - Target statement number (as target_line_number)
    #     - Status (Source Only, Target Only, or Mapped)

    #     Args:
    #         state: Current workflow state containing source_sql_statements and target_sql_statements

    #     Returns:
    #         Dictionary containing the mappings between source and target statements
    #     """
    #     try:
    #         # Get the source and target statements from the state
    #         source_statements = state.source_sql_statements
    #         target_statements = state.target_sql_statements

    #         # Create an instance of the EmbeddingMapper
    #         mapper = EmbeddingMapper()

    #         # Generate mappings using embeddings and semantic search
    #         mappings = mapper.map_statements(source_statements, target_statements)

    #         # Save the mappings to a CSV file
    #         self.save_mappings_to_csv(mappings)

    #         # Return the mappings
    #         return {"mappings_statements": mappings}
    #     except Exception as e:
    #         print(f"Error in sql_mappings node: {str(e)}")
    #         return {"error_message": f"Error creating SQL mappings: {str(e)}"}

    def identify_error_fix(self, state: WorkflowState) -> Dict[str, Any]:
        """Identify the error in the target code based on the deployment error message and fix it.

        This node analyzes the source code, target code, and deployment error message to:
        1. Identify the specific error in the target code
        2. Fix the error based on the source code and error message
        3. Return both the original and fixed target code

        Args:
            state: Current workflow state containing source_code, target_code, and deployment_error_message

        Returns:
            Dictionary containing:
            - original_target_code: The original target code before fixing
            - target_code: The fixed target code with errors corrected
        """
        try:
            # Get the source code, target code, and error message from the state
            source_code = state.source_code
            target_code = state.target_code
            error_message = state.deployment_error_message

            # Create a prompt for the LLM to identify and fix the error
            prompt = identify_error_fix_prompt(source_code, target_code, error_message)

            # Use the LLM with structured output to identify and fix the error
            structured_llm = self.llm.client.with_structured_output(FixedCodeOutput)
            result = structured_llm.invoke(prompt)
            return {
                "fixed_code": result.fixed_code
            }
        except Exception as e:
            print(f"Error in identify_error_fix node: {str(e)}")
            return {"error_message": f"Error fixing target code: {str(e)}"}

    def review_fixed_code(self, state: WorkflowState) -> Dict[str, Any]:
        """Review the fixed code to ensure it properly addresses the error without introducing new issues.

        This node analyzes the source code, original target code, fixed code, and deployment error message to:
        1. Verify that the fixed code properly addresses the specific error
        2. Check if the fix is minimal and only changes what's necessary
        3. Ensure that the fix is consistent across all similar instances
        4. Verify that no unnecessary changes were made
        5. Check that no code was removed or commented out unnecessarily
        6. Ensure that all comments from the original code are preserved
        7. Verify that no duplicate lines or redundant code was added
        8. Pay special attention to exception handling blocks - ensure they are properly structured
        9. If multiple exception handlers exist, ensure they are all properly maintained

        Args:
            state: Current workflow state containing source_code, target_code, fixed_code, and deployment_error_message

        Returns:
            Dictionary containing the review results
        """
        try:
            # Get the source code, target code, fixed code, and error message from the state
            source_code = state.source_code
            target_code = state.target_code
            fixed_code = state.fixed_code
            error_message = state.deployment_error_message

            # Create a prompt for the LLM to review the fixed code
            prompt = review_fixed_code_prompt(source_code, target_code, fixed_code, error_message)

            # Use the LLM with structured output to review the fixed code
            structured_llm = self.llm.client.with_structured_output(ReviewFixedCodeOutput)
            result = structured_llm.invoke(prompt)

            # Return the review results
            return {
                "review_results": {
                    "is_properly_fixed": result.is_properly_fixed,
                    "review_comments": result.review_comments,
                    "issues_found": result.issues_found,
                    "recommendations": result.recommendations
                }
            }
        except Exception as e:
            print(f"Error in review_fixed_code node: {str(e)}")
            return {"error_message": f"Error reviewing fixed code: {str(e)}"}

    # def save_mappings_to_csv(self, mappings: List["MappingStatement"]) -> None:
    #     """Save SQL statement mappings to a CSV file.

    #     This method takes a list of MappingStatement objects and saves them to a CSV file
    #     with columns for source statement, source line number, target statement, target line number,
    #     and status.

    #     Args:
    #         mappings: List of MappingStatement objects

    #     Returns:
    #         None
    #     """
    #     try:
    #         import csv
    #         import os
    #         from datetime import datetime

    #         # Create output directory if it doesn't exist
    #         output_dir = "output"
    #         if not os.path.exists(output_dir):
    #             os.makedirs(output_dir)

    #         # Generate a filename with timestamp
    #         timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    #         filename = os.path.join(output_dir, f"sql_mappings_{timestamp}.csv")

    #         # Write the mappings to the CSV file
    #         with open(filename, 'w', newline='') as csvfile:
    #             # Define the CSV columns
    #             fieldnames = ['Source Statement', 'Source Number', 'Target Statement', 'Target Number', 'Status']
    #             writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

    #             # Write the header
    #             writer.writeheader()

    #             # Write each mapping as a row
    #             for mapping in mappings:
    #                 writer.writerow({
    #                     'Source Statement': mapping.source_statement,
    #                     'Source Number': mapping.source_line_number,
    #                     'Target Statement': mapping.target_statement,
    #                     'Target Number': mapping.target_line_number,
    #                     'Status': mapping.status
    #                 })

    #         print(f"SQL mappings saved to {filename}")
    #     except Exception as e:
    #         print(f"Error saving SQL mappings to CSV: {str(e)}")
