"""
File Tools - Tools for interacting with the file system
"""
import os
import logging
from typing import List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def save_file(file_path: str, file_content: str, add_last_line_newline: bool = True) -> str:
    """
    Save content to a file
    
    Args:
        file_path: Path where to save the file
        file_content: Content to write to the file
        add_last_line_newline: Whether to add a newline at the end
        
    Returns:
        Success message with file path
    """
    try:
        # Create directories if they don't exist
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        
        # Add newline at the end if requested
        if add_last_line_newline and not file_content.endswith('\n'):
            file_content += '\n'
            
        # Write content to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(file_content)
            
        return f"File saved successfully: {file_path}"
    
    except Exception as e:
        error_msg = f"Error saving file {file_path}: {str(e)}"
        logger.error(error_msg)
        return error_msg

def read_file(file_path: str) -> str:
    """
    Read content from a file
    
    Args:
        file_path: Path of the file to read
        
    Returns:
        Content of the file
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        return content
    
    except Exception as e:
        error_msg = f"Error reading file {file_path}: {str(e)}"
        logger.error(error_msg)
        return error_msg

def list_files(directory: str = ".", pattern: Optional[str] = None) -> str:
    """
    List files in a directory, optionally filtered by a pattern
    
    Args:
        directory: Directory to list files from
        pattern: Optional glob pattern to filter files
        
    Returns:
        List of files in the directory
    """
    try:
        import glob
        
        if not os.path.exists(directory):
            return f"Error: Directory not found: {directory}"
            
        if not os.path.isdir(directory):
            return f"Error: Not a directory: {directory}"
            
        if pattern:
            files = glob.glob(os.path.join(directory, pattern))
        else:
            files = os.listdir(directory)
            files = [os.path.join(directory, f) for f in files]
            
        # Format the output
        result = "Files:\n"
        for file in sorted(files):
            if os.path.isdir(file):
                result += f"📁 {file}/\n"
            else:
                result += f"📄 {file}\n"
                
        return result
    
    except Exception as e:
        error_msg = f"Error listing files in {directory}: {str(e)}"
        logger.error(error_msg)
        return error_msg

def edit_file(file_path: str, old_str: str, new_str: str, 
             line_start: Optional[int] = None, line_end: Optional[int] = None) -> str:
    """
    Edit a file by replacing text
    
    Args:
        file_path: Path of the file to edit
        old_str: String to replace
        new_str: Replacement string
        line_start: Optional start line number (1-based)
        line_end: Optional end line number (1-based)
        
    Returns:
        Result of the edit operation
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"
            
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # If line numbers are specified, only replace in that range
        if line_start is not None and line_end is not None:
            if line_start < 1 or line_end > len(lines) or line_start > line_end:
                return f"Error: Invalid line range: {line_start}-{line_end}"
                
            # Convert to 0-based indexing
            line_start -= 1
            line_end -= 1
            
            # Join the lines in the specified range
            section = ''.join(lines[line_start:line_end+1])
            
            # Replace the text in the section
            if old_str not in section:
                return f"Error: Text not found in the specified line range"
                
            new_section = section.replace(old_str, new_str)
            
            # Update the lines
            lines[line_start:line_end+1] = new_section.splitlines(True)
        else:
            # Replace in the entire file
            content = ''.join(lines)
            
            if old_str not in content:
                return f"Error: Text not found in the file"
                
            new_content = content.replace(old_str, new_str)
            lines = new_content.splitlines(True)
            
        # Write the updated content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
            
        return f"File edited successfully: {file_path}"
    
    except Exception as e:
        error_msg = f"Error editing file {file_path}: {str(e)}"
        logger.error(error_msg)
        return error_msg

def remove_files(file_paths: List[str]) -> str:
    """
    Remove files
    
    Args:
        file_paths: List of file paths to remove
        
    Returns:
        Result of the removal operation
    """
    results = []
    
    for file_path in file_paths:
        try:
            if not os.path.exists(file_path):
                results.append(f"Error: File not found: {file_path}")
                continue
                
            if os.path.isdir(file_path):
                import shutil
                shutil.rmtree(file_path)
                results.append(f"Directory removed: {file_path}")
            else:
                os.remove(file_path)
                results.append(f"File removed: {file_path}")
        
        except Exception as e:
            error_msg = f"Error removing {file_path}: {str(e)}"
            logger.error(error_msg)
            results.append(error_msg)
            
    return "\n".join(results)
