"""
Memory Embeddings - Provides vector embeddings for semantic memory search
"""
import numpy as np
from typing import List, Dict, Any, Optional, Union, Tuple
import logging
import re
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleEmbedder:
    """
    A simple text embedder that creates vector representations of text
    This is a simplified version that doesn't require external dependencies
    In a production system, you would use a proper embedding model
    """
    
    def __init__(self, dimensions: int = 100, vocabulary_size: int = 10000):
        """
        Initialize the embedder
        
        Args:
            dimensions: Dimensionality of the embedding vectors
            vocabulary_size: Maximum vocabulary size to consider
        """
        self.dimensions = dimensions
        self.vocabulary_size = vocabulary_size
        self.word_vectors = {}
        self.word_counts = {}
        
    def _preprocess_text(self, text: str) -> List[str]:
        """
        Preprocess text for embedding
        
        Args:
            text: Input text
            
        Returns:
            List of preprocessed tokens
        """
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters and replace with space
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # Split into tokens and filter out short words
        tokens = [token for token in text.split() if len(token) > 2]
        
        return tokens
        
    def _get_or_create_vector(self, word: str) -> np.ndarray:
        """
        Get or create a vector for a word
        
        Args:
            word: Input word
            
        Returns:
            Vector representation of the word
        """
        if word not in self.word_vectors:
            # Create a deterministic but seemingly random vector based on the word
            # This ensures the same word always gets the same vector
            seed = sum(ord(c) for c in word)
            np.random.seed(seed)
            self.word_vectors[word] = np.random.randn(self.dimensions)
            self.word_counts[word] = 0
            
        # Increment word count
        self.word_counts[word] += 1
        
        return self.word_vectors[word]
        
    def embed_text(self, text: str) -> np.ndarray:
        """
        Create an embedding vector for a text
        
        Args:
            text: Input text
            
        Returns:
            Embedding vector
        """
        tokens = self._preprocess_text(text)
        
        if not tokens:
            # Return zero vector for empty text
            return np.zeros(self.dimensions)
            
        # Get vectors for all tokens
        vectors = [self._get_or_create_vector(token) for token in tokens]
        
        # Average the vectors (weighted by TF-IDF-like scoring)
        weights = []
        for token in tokens:
            # Simple TF-IDF approximation: more weight to less common words
            count = self.word_counts[token]
            weight = 1.0 / (1.0 + np.log(1.0 + count))
            weights.append(weight)
            
        # Normalize weights
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
            
        # Weighted average
        embedding = np.zeros(self.dimensions)
        for i, vector in enumerate(vectors):
            embedding += vector * weights[i]
            
        # Normalize to unit length
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
            
        return embedding
        
    def similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """
        Calculate cosine similarity between two vectors
        
        Args:
            vec1: First vector
            vec2: Second vector
            
        Returns:
            Cosine similarity (between -1 and 1)
        """
        # Ensure vectors are normalized
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        vec1_norm = vec1 / norm1
        vec2_norm = vec2 / norm2
        
        return float(np.dot(vec1_norm, vec2_norm))

# Global embedder instance
_embedder = None

def get_embedder() -> SimpleEmbedder:
    """Get or create the global embedder instance"""
    global _embedder
    
    if _embedder is None:
        _embedder = SimpleEmbedder()
        
    return _embedder
