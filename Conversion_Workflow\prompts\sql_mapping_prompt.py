"""
Prompts for SQL statement mapping in Oracle to PostgreSQL conversion.
"""

def create_sql_mapping_prompt(source_statements: list, target_statements: list) -> str:
    """
    Creates a prompt for mapping Oracle source statements to PostgreSQL target statements.

    Args:
        source_statements: List of Oracle SQL statements
        target_statements: List of PostgreSQL SQL statements

    Returns:
        A formatted prompt string for the LLM
    """
    return f"""
    You are a SQL migration expert. Analyze these Oracle source statements and PostgreSQL target statements
    and create a detailed mapping between them.
    
    Source SQL statements:
    {source_statements}
    
    Target SQL statements:
    {target_statements}
    
    For each statement, determine if it exists in both source and target (Mapped),
    only in source (Source Only), or only in target (Target Only).
    
    Return the mappings in the following format:
    {{
        "mappings": [
            {{
                "source_statement": "statement text",
                "source_line_number": "1",
                "target_statement": "statement text",
                "target_line_number": "1",
                "status": "Mapped"
            }},
            {{
                "source_statement": "statement text",
                "source_line_number": "2",
                "target_statement": "",
                "target_line_number": "",
                "status": "Source Only"
            }},
            {{
                "source_statement": "",
                "source_line_number": "",
                "target_statement": "statement text",
                "target_line_number": "2",
                "status": "Target Only"
            }}
        ]
    }}
    
    IMPORTANT:
    1. Preserve the order of statements on both sides
    2. Include ALL statements from both source and target
    3. For "Source Only" statements, leave target fields empty
    4. For "Target Only" statements, leave source fields empty
    5. For "Mapped" statements, include both source and target information
    6. Use the statement index (1-based) as the line number
    """
