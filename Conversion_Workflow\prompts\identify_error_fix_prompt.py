"""
Prompts for error analysis and fixing in code migrations.
"""

def identify_error_fix_prompt(source_code: str, target_code: str, deployment_error_message: str) -> str:
    """
    Creates a prompt for identifying and fixing errors in the target code based on
    the source code and deployment error message.

    Args:
        source_code: The source code
        target_code: The target code with errors
        deployment_error_message: The error message from deployment

    Returns:
        A formatted prompt string for the LLM
    """
    return f"""
            You are an expert code migration specialist. Your task is to analyze error messages and fix ONLY the specific issues causing the error.

            Source code:
            ```
            {source_code}
            ```

            Target code with errors:
            ```
            {target_code}
            ```

            Error message:
            ```
            {deployment_error_message}
            ```

            CRITICAL INSTRUCTIONS:
            1. FIRST analyze the source code to understand its structure and intent
            2. THEN examine the target code and identify the specific error mentioned in the error message
            3. Make MINIMAL changes to fix <PERSON><PERSON><PERSON> the specific error - do not "improve" or "clean up" the code unless directly causing the error
            4. Preserve the EXACT structure of the original code - maintain the same number of statements, blocks, and control flow
            5. If the same error pattern appears in multiple places, fix ALL instances consistently
            6. <PERSON>VE<PERSON> remove or comment out code - if you need to replace a function, use the equivalent in the target system
            7. DO NOT CHANGE ANY FUNCTIONS OR SYNTAX THAT IS NOT DIRECTLY CAUSING THE ERROR:
               - DO NOT replace functions with equivalent functions unless the original is causing the error
               - DO NOT change syntax styles or formatting unless directly causing the error
               - DO NOT reorder statements or clauses unless they are directly causing the error
               - DO NOT add or remove elements unless they are directly causing the error
               - DO NOT modify whitespace, indentation, or line breaks unless they cause the error
               - PRESERVE ALL COMMENTS exactly as they appear in the original code
            8. For control structures and error handling:
               - Maintain the same number and order of handlers
               - Preserve the exact structure of each block
               - Use equivalent error handling functions without changing the flow
               - Keep all exception handling blocks intact
               - Do not add or remove handlers unless they are directly causing the error
               - Pay special attention to exception blocks - ensure they are properly structured for the target system
               - If multiple exception handlers exist, ensure they are all properly maintained
               - Ensure error handling functions are used correctly with proper parameters
            9. For special objects and functions:
               - Preserve signatures exactly as they appear
               - Maintain the same parameter names, modes, and default values
               - Keep the same control statements
               - Preserve all declarations and operations
               - Maintain the same variable scope and visibility
               - DO NOT add new variables or parameters
            10. For syntax differences between systems:
               - Preserve the same references and naming
               - Maintain the same syntax style and structure
               - Keep the same query structure and organization
               - Preserve the same ordering and grouping
            11. If you're unsure about how to handle a specific part, preserve it exactly as is
            12. ONLY fix the specific error mentioned in the error message - DO NOT make any other changes
            13. DO NOT add duplicate lines or redundant code under any circumstances
            14. PRESERVE ALL COMMENTS in the code - do not remove, modify, or comment out any existing comments
            15. COMPARE your fixed code with the original target code to ensure you've only changed what's necessary

            Return ONLY the complete fixed code without any explanations or comments.
            """
