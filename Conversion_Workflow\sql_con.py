# from langchain_openai import AzureChatOpenAI
# from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
# from langchain_community.utilities import SQLDatabase
# from langchain.agents import AgentExecutor, create_openai_tools_agent
# from langchain import hub
# from langchain_core.tools import tool

# # Initialize the LLM
# llm = AzureChatOpenAI(
#     azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
#     azure_deployment="gpt4-deployment",
#     api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
#     api_version="2024-12-01-preview",
#     temperature=0.3,
# )
# print("LLM initialized")

# # Create a SQLDatabase connection
# # You can modify the connection string to match your database
# db = SQLDatabase.from_uri(
#     'postgresql+psycopg2://postgres:postgres@localhost/postgres'
# )
# print("Database connected")

# # Initialize the SQLDatabaseToolkit
# toolkit = SQLDatabaseToolkit(db=db, llm=llm)
# print("SQL toolkit created")

# # Define a custom tool for getting database tables
# @tool
# def get_database_size() -> dict:
#     """Get the total size of the database.

#     This tool returns the size of the database in megabytes.
#     Use this to check database storage usage.

#     Returns:
#         A dictionary with the database size information
#     """
#     # Use the existing db connection
#     result = db.run("""
#         SELECT
#             pg_size_pretty(pg_database_size(current_database())) as size,
#             pg_database_size(current_database()) as size_bytes
#     """)
#     return result

# @tool
# def get_database_tables():
#     """Get the list of tables in the database.

#     This tool directly connects to the database and returns all table names.
#     Use this when you need a simple list of tables without additional schema information.

#     Returns:
#         A list of dictionaries containing table names
#     """
#     # Use the existing db connection
#     result = db.run("""
#         SELECT table_name
#         FROM information_schema.tables
#         WHERE table_type = 'BASE TABLE'
#         AND table_schema NOT IN ('pg_catalog', 'information_schema');
#     """)
#     tables_list = [row.strip() for row in result.split('\n') if row.strip()]
#     return tables_list


# # Get the OpenAI functions agent prompt
# prompt = hub.pull("hwchase17/openai-functions-agent")

# # Define custom tools
# custom_tools = [get_database_size, get_database_tables]

# # Get all tools from the SQLDatabaseToolkit
# sql_tools = toolkit.get_tools()
# print(f"SQL toolkit tools: {len(sql_tools)} tools available")

# # Combine with custom tools
# all_tools = sql_tools + custom_tools
# print(f"Combined tools: {len(all_tools)} tools available")
# print(f"Combined tools: {[i.name for i in all_tools]}")

# # Create a new agent with all tools
# combined_agent = create_openai_tools_agent(llm, all_tools, prompt)
# agent_executor = AgentExecutor(
#     agent=combined_agent,
#     tools=all_tools,
#     verbose=True,
#     handle_parsing_errors=True,
#     max_iterations=10
# )
# print("Combined SQL agent created with both SQLDatabaseToolkit and custom tools")


# print("\n" + "-"*60)
# print("INTERACTIVE MODE INSTRUCTIONS:")
# print("-"*60)
# print("Type 'exit' or 'quit' to end the session")
# print("Type 'help' to see example queries")
# print("-"*60)

# # Start interactive loop
# while True:
#     user_input = input("\nEnter your database query: ")

#     # Handle special commands
#     if user_input.lower() in ['exit', 'quit']:
#         print("Exiting SQL agent...")
#         break

#     elif user_input.lower() in ['help', '?']:
#         print("\nExample queries you can try:")
#         print("1. List all tables in the database")
#         print("2. Show me the schema of the [table_name] table")
#         print("3. How many rows are in each table?")
#         print("4. What is the total size of the database?")
#         print("5. Find all records in [table_name] where [column] = [value]")
#         print("6. What are the largest tables in the database?")
#         print("7. Show me the first 5 rows from [table_name]")
#         print("8. Analyze the performance of the database")
#         continue

#     # Process normal queries
#     try:
#         print("\nProcessing your query...")
#         response = agent_executor.invoke({"input": user_input})
#         print("\nResponse:")
#         print(response["output"])
#     except Exception as e:
#         print(f"Error processing query: {str(e)}")
#         print("Try rephrasing your query or type 'help' for examples.")
















from langchain_openai import AzureChatOpenAI
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor
from langchain.agents.react.agent import create_react_agent
from langchain_core.prompts import PromptTemplate
from langchain_core.tools import tool

# Initialize the LLM
llm = AzureChatOpenAI(
    azure_endpoint="https://ai-testgeneration707727059630.openai.azure.com/",
    azure_deployment="gpt4-deployment",
    api_key="wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm",
    api_version="2024-12-01-preview",
    temperature=0.3,
)
print("LLM initialized")

# Create a SQLDatabase connection
# You can modify the connection string to match your database
db = SQLDatabase.from_uri(
    'postgresql+psycopg2://postgres:postgres@localhost/postgres'
)
print("Database connected")

# Initialize the SQLDatabaseToolkit
toolkit = SQLDatabaseToolkit(db=db, llm=llm)
print("SQL toolkit created")

# Define a custom tool for getting database tables
@tool
def get_database_size() -> dict:
    """Get the total size of the database.

    This tool returns the size of the database in megabytes.
    Use this to check database storage usage.

    Returns:
        A dictionary with the database size information
    """
    # Use the existing db connection
    result = db.run("""
        SELECT
            pg_size_pretty(pg_database_size(current_database())) as size,
            pg_database_size(current_database()) as size_bytes
    """)
    return result

@tool
def get_database_tables():
    """Get the list of tables in the database.

    This tool directly connects to the database and returns all table names.
    Use this when you need a simple list of tables without additional schema information.

    Returns:
        A list of dictionaries containing table names
    """
    # Use the existing db connection
    result = db.run("""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_type = 'BASE TABLE'
        AND table_schema NOT IN ('pg_catalog', 'information_schema');
    """)
    return result


# Define custom tools
custom_tools = [get_database_size, get_database_tables]

# Get all tools from the SQLDatabaseToolkit
sql_tools = toolkit.get_tools()
print(f"SQL toolkit tools: {len(sql_tools)} tools available")

# Combine with custom tools
all_tools = sql_tools + custom_tools
print(f"Combined tools: {len(all_tools)} tools available")
print(f"Tool names: {[tool.name for tool in all_tools]}")

# Create a ReAct agent prompt template
react_prompt = PromptTemplate.from_template("""
You are an expert SQL database analyst with access to a PostgreSQL database.
Your job is to help users understand the database structure, run queries, and analyze data.

You have access to the following tools:

{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: {agent_scratchpad}
""")

# Create a ReAct agent
react_agent = create_react_agent(
    llm=llm,
    tools=all_tools,
    prompt=react_prompt
)

# Create the agent executor
agent_executor = AgentExecutor(
    agent=react_agent,
    tools=all_tools,
    verbose=True,
    handle_parsing_errors=True,
    max_iterations=10
)



try:
    print("\nProcessing your query...")
    response = agent_executor.invoke({"input": 
        """please fix the below issue in the target code.
        issue:
            mismatched parentheses at or near ";"

        target postgresql code:
                SET search_path TO HL7;

    CREATE OR REPLACE PROCEDURE hl7.P_SAVESUBDATATYPE (v_SUBDATATYPELIST IN varchar)
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $BODY$
    DECLARE
        v_Row_Delimiter char(1);
        v_Col_Delimiter char(1);
        v_Optionality varchar(50);
        v_Col_DelimiterIndex int;
        v_Row_DelimiterIndex int;
        v_RowCount int;
        v_SubDataType_List varchar(1000);
        v_SubDataTypeID varchar(100);
        tempString varchar(1000);
        updateQuery varchar(2000);
        v_Col_Delimiter2 int;
        --
        -- Author : TCS
        -- Created Date : 24-August-2007
        -- Description : This Stored Procedure saves the subdatatype information.
        --
    BEGIN
        SET search_path TO HL7;
        v_Row_Delimiter := '|';
        v_Col_Delimiter := ',';
        v_SubDataType_List := v_SUBDATATYPELIST;
        IF LENGTH(TRIM(v_SubDataType_List)) != 0 THEN
            LOOP
                SELECT
                    public.instr (v_SubDataType_List, '|') INTO STRICT v_Row_DelimiterIndex;
                exit
                WHEN v_Row_DelimiterIndex = 0;
                SELECT
                    public.instr (v_SubDataType_List, ',') INTO STRICT v_Col_DelimiterIndex;
                SELECT
                    substr(v_SubDataType_List, 0, v_Col_DelimiterIndex - 1) INTO STRICT v_SubDataTypeID;
                tempString := Substr(v_SubDataType_List, v_Col_DelimiterIndex + 1, Length(v_SubDataType_List));
                v_Col_Delimiter2 := public.instr (tempString, '|');
                v_Optionality := Substr(tempString, 1, v_Col_Delimiter2 - 1);
                updateQuery := 'UPDATE hl7.HL7_STD_SUBDATATYPE SET OPTIONALITY = ''' || v_Optionality || ''' WHERE SUBDATATYPEID=''' || v_SubDataTypeID || '''';
                RAISE NOTICE '%', SUBSTR(updateQuery, 9, 257);
                EXECUTE updateQuery;
                v_SubDataType_List := SUBSTR(v_SubDataType_List, v_Row_DelimiterIndex + 1, Length(v_SubDataType_List));
                RAISE NOTICE 'v_SubDataType_List= %', v_SubDataType_List;
                exit
                WHEN v_SubDataType_List IS NULL;
                RAISE NOTICE '%', LENGTH(TRIM(v_SubDataType_List);
            END LOOP;
        END IF;
    END;
    $BODY$;
        """
        })
    print("\nResponse:")
    print(response["output"])
except Exception as e:
    print(f"Error processing query: {str(e)}")
    print("Try rephrasing your query or type 'help' for examples.")
