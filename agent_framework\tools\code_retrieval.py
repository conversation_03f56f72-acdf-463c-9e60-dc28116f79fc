"""
Code Retrieval Tool - Advanced codebase search and understanding
"""
import os
import re
import logging
import json
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CodebaseIndex:
    """
    Simple index for codebase retrieval
    """

    def __init__(self, root_dir: str = "."):
        """
        Initialize the codebase index

        Args:
            root_dir: Root directory of the codebase
        """
        self.root_dir = os.path.abspath(root_dir)
        self.file_index = {}
        self.symbol_index = {}
        self.ignore_patterns = [
            r'\.git',
            r'\.vscode',
            r'__pycache__',
            r'\.pyc$',
            r'\.pyo$',
            r'\.pyd$',
            r'\.so$',
            r'\.dll$',
            r'\.exe$',
            r'\.bin$',
            r'node_modules',
            r'\.venv',
            r'venv',
            r'env',
            r'\.env',
            r'\.DS_Store',
        ]

    def should_ignore(self, path: str) -> bool:
        """Check if a path should be ignored"""
        for pattern in self.ignore_patterns:
            if re.search(pattern, path):
                return True
        return False

    def build_index(self) -> None:
        """Build the codebase index"""
        logger.info(f"Building codebase index from {self.root_dir}")

        for root, dirs, files in os.walk(self.root_dir):
            # Filter out directories to ignore
            dirs[:] = [d for d in dirs if not self.should_ignore(os.path.join(root, d))]

            for file in files:
                file_path = os.path.join(root, file)

                if self.should_ignore(file_path):
                    continue

                # Only index text files
                if not self._is_text_file(file_path):
                    continue

                try:
                    rel_path = os.path.relpath(file_path, self.root_dir)

                    # Read file content
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # Store in file index
                    self.file_index[rel_path] = {
                        'content': content,
                        'size': len(content),
                        'extension': os.path.splitext(file_path)[1],
                        'symbols': self._extract_symbols(content, os.path.splitext(file_path)[1])
                    }

                    # Update symbol index
                    for symbol in self.file_index[rel_path]['symbols']:
                        if symbol['name'] not in self.symbol_index:
                            self.symbol_index[symbol['name']] = []

                        self.symbol_index[symbol['name']].append({
                            'file': rel_path,
                            'type': symbol['type'],
                            'line': symbol['line']
                        })

                except Exception as e:
                    logger.warning(f"Error indexing {file_path}: {str(e)}")

        logger.info(f"Indexed {len(self.file_index)} files and {len(self.symbol_index)} symbols")

    def _is_text_file(self, file_path: str) -> bool:
        """Check if a file is a text file"""
        # Check extension first
        _, ext = os.path.splitext(file_path)
        text_extensions = [
            '.py', '.js', '.ts', '.html', '.css', '.json', '.md', '.txt',
            '.java', '.c', '.cpp', '.h', '.cs', '.php', '.rb', '.go', '.rs',
            '.sh', '.bat', '.ps1', '.yaml', '.yml', '.toml', '.ini', '.cfg'
        ]

        if ext.lower() in text_extensions:
            return True

        # Try to read the file as text
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.read(1024)  # Read a small chunk
            return True
        except:
            return False

    def _extract_symbols(self, content: str, extension: str) -> List[Dict[str, Any]]:
        """Extract symbols from file content based on file type"""
        symbols = []

        # Python
        if extension.lower() == '.py':
            # Extract classes
            class_pattern = r'^\s*class\s+(\w+)'
            for match in re.finditer(class_pattern, content, re.MULTILINE):
                line_number = content[:match.start()].count('\n') + 1
                symbols.append({
                    'name': match.group(1),
                    'type': 'class',
                    'line': line_number
                })

            # Extract functions
            func_pattern = r'^\s*def\s+(\w+)'
            for match in re.finditer(func_pattern, content, re.MULTILINE):
                line_number = content[:match.start()].count('\n') + 1
                symbols.append({
                    'name': match.group(1),
                    'type': 'function',
                    'line': line_number
                })

        # JavaScript/TypeScript
        elif extension.lower() in ['.js', '.ts']:
            # Extract classes
            class_pattern = r'^\s*class\s+(\w+)'
            for match in re.finditer(class_pattern, content, re.MULTILINE):
                line_number = content[:match.start()].count('\n') + 1
                symbols.append({
                    'name': match.group(1),
                    'type': 'class',
                    'line': line_number
                })

            # Extract functions
            func_pattern = r'^\s*(?:function|const|let|var)\s+(\w+)\s*(?:=\s*(?:function|\([^)]*\)\s*=>)|[^=]*\()'
            for match in re.finditer(func_pattern, content, re.MULTILINE):
                line_number = content[:match.start()].count('\n') + 1
                symbols.append({
                    'name': match.group(1),
                    'type': 'function',
                    'line': line_number
                })

        return symbols

    def search(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """
        Search the codebase for the query

        Args:
            query: Search query
            max_results: Maximum number of results to return

        Returns:
            List of search results
        """
        results = []

        # Split query into keywords for better matching
        keywords = [k.lower() for k in query.split() if len(k) > 2]

        # Calculate relevance scores for each file
        file_scores = {}
        for file_path, file_info in self.file_index.items():
            content = file_info['content'].lower()

            # Calculate base score based on keyword matches
            score = 0
            matches = []

            # Check for exact phrase match (highest priority)
            query_lower = query.lower()
            if query_lower in content:
                score += 10
                # Find all occurrences of the exact phrase
                for match in re.finditer(re.escape(query_lower), content):
                    match_index = match.start()
                    line_number = content[:match_index].count('\n') + 1
                    matches.append((line_number, 10))  # Store line number and match score

            # Check for keyword matches
            for keyword in keywords:
                if keyword in content:
                    # Count occurrences of the keyword
                    occurrences = content.count(keyword)
                    keyword_score = min(occurrences, 5) * 0.5  # Cap at 2.5 points per keyword
                    score += keyword_score

                    # Find all occurrences of this keyword
                    for match in re.finditer(re.escape(keyword), content):
                        match_index = match.start()
                        line_number = content[:match_index].count('\n') + 1
                        matches.append((line_number, keyword_score / occurrences))  # Distribute score across occurrences

            # Check file extension relevance
            ext = file_info['extension'].lower()
            if ext in ['.py', '.js', '.ts', '.java', '.c', '.cpp', '.cs']:
                score *= 1.2  # Boost score for code files

            # If we have matches, store the file score and match information
            if score > 0:
                file_scores[file_path] = {
                    'score': score,
                    'matches': matches,
                    'content': content
                }

        # Search in symbols with more sophisticated matching
        for symbol, occurrences in self.symbol_index.items():
            symbol_lower = symbol.lower()

            # Calculate symbol match score
            symbol_score = 0

            # Exact symbol match
            if query_lower == symbol_lower:
                symbol_score = 15  # Highest priority for exact symbol match
            # Symbol starts with query
            elif symbol_lower.startswith(query_lower):
                symbol_score = 8
            # Query is contained in symbol
            elif query_lower in symbol_lower:
                symbol_score = 5
            # Check keyword matches in symbol
            else:
                for keyword in keywords:
                    if keyword in symbol_lower:
                        symbol_score += 2

            # If symbol matches, add to file scores
            if symbol_score > 0:
                for occurrence in occurrences:
                    file_path = occurrence['file']
                    line_number = occurrence['line']

                    # Initialize file score if not already present
                    if file_path not in file_scores:
                        if file_path in self.file_index:
                            file_scores[file_path] = {
                                'score': 0,
                                'matches': [],
                                'content': self.file_index[file_path]['content'].lower()
                            }
                        else:
                            continue

                    # Add symbol match to file's matches
                    file_scores[file_path]['matches'].append((line_number, symbol_score))
                    file_scores[file_path]['score'] += symbol_score

        # Process the scored files to generate results
        for file_path, file_data in sorted(file_scores.items(), key=lambda x: x[1]['score'], reverse=True):
            if len(results) >= max_results:
                break

            content = file_data['content']
            lines = content.splitlines()

            # Group nearby matches to avoid duplicate contexts
            matches = sorted(file_data['matches'], key=lambda x: x[0])  # Sort by line number
            grouped_matches = []
            current_group = []

            for match in matches:
                if not current_group or match[0] - current_group[-1][0] <= 5:  # If within 5 lines of previous match
                    current_group.append(match)
                else:
                    grouped_matches.append(current_group)
                    current_group = [match]

            if current_group:
                grouped_matches.append(current_group)

            # Process each group of matches
            for group in grouped_matches:
                if len(results) >= max_results:
                    break

                # Find the line range for this group
                start_line = max(0, min(m[0] for m in group) - 3)
                end_line = min(len(lines), max(m[0] for m in group) + 3)

                # Calculate the group score (sum of match scores)
                group_score = sum(m[1] for m in group)

                # Get context lines
                if start_line < len(lines) and end_line <= len(lines):
                    context = "\n".join(lines[start_line:end_line])

                    # Add to results
                    results.append({
                        'file': file_path,
                        'line': min(m[0] for m in group),  # Use the first match line as reference
                        'context': context,
                        'score': group_score,
                        'start_line': start_line + 1,  # Convert to 1-based indexing
                        'end_line': end_line
                    })

        # Sort by score and limit results
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:max_results]

    def get_file_content(self, file_path: str) -> Optional[str]:
        """Get the content of a file"""
        rel_path = os.path.relpath(os.path.abspath(file_path), self.root_dir)

        if rel_path in self.file_index:
            return self.file_index[rel_path]['content']

        # Try to read the file directly if not in index
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except:
            return None

    def find_symbol(self, symbol_name: str) -> List[Dict[str, Any]]:
        """Find a symbol in the codebase"""
        if symbol_name in self.symbol_index:
            return self.symbol_index[symbol_name]
        return []

# Global index instance
_codebase_index = None

def get_codebase_index(root_dir: str = ".") -> CodebaseIndex:
    """Get or create the codebase index"""
    global _codebase_index

    if _codebase_index is None:
        _codebase_index = CodebaseIndex(root_dir)
        _codebase_index.build_index()

    return _codebase_index

def codebase_retrieval(information_request: str, root_dir: str = ".", max_results: int = 5) -> str:
    """
    Retrieve information from the codebase with advanced context understanding

    Args:
        information_request: Description of the information needed
        root_dir: Root directory of the codebase
        max_results: Maximum number of results to return

    Returns:
        Retrieved information in a formatted string with detailed context
    """
    try:
        # Get or create the index
        index = get_codebase_index(root_dir)

        # Search the codebase
        results = index.search(information_request, max_results)

        if not results:
            return f"No results found for: {information_request}"

        # Format the results with more detailed information
        formatted_results = f"# Codebase Search Results for: {information_request}\n\n"
        formatted_results += f"Found {len(results)} relevant code snippets.\n\n"

        for i, result in enumerate(results, 1):
            file_path = result['file']
            file_ext = os.path.splitext(file_path)[1]

            # Determine language for syntax highlighting
            language = ""
            if file_ext in ['.py']:
                language = "python"
            elif file_ext in ['.js']:
                language = "javascript"
            elif file_ext in ['.ts']:
                language = "typescript"
            elif file_ext in ['.java']:
                language = "java"
            elif file_ext in ['.c', '.cpp', '.h']:
                language = "cpp"
            elif file_ext in ['.cs']:
                language = "csharp"
            elif file_ext in ['.html']:
                language = "html"
            elif file_ext in ['.css']:
                language = "css"
            elif file_ext in ['.json']:
                language = "json"
            elif file_ext in ['.md']:
                language = "markdown"

            # Add file information
            formatted_results += f"## Result {i}: {file_path}\n"
            formatted_results += f"**Relevance Score**: {result['score']:.2f}\n"
            formatted_results += f"**Lines**: {result.get('start_line', result['line'])}-{result.get('end_line', result['line'] + 5)}\n\n"

            # Add code context with syntax highlighting
            formatted_results += f"```{language}\n"
            formatted_results += result['context']
            formatted_results += "\n```\n\n"

            # Try to add additional context about the file
            try:
                # Get symbols from this file
                file_symbols = []
                for symbol_name, occurrences in index.symbol_index.items():
                    for occurrence in occurrences:
                        if occurrence['file'] == file_path:
                            file_symbols.append({
                                'name': symbol_name,
                                'type': occurrence['type'],
                                'line': occurrence['line']
                            })

                # If we have symbols, add them to the result
                if file_symbols:
                    formatted_results += "**File contains these symbols:**\n"

                    # Group symbols by type
                    symbols_by_type = {}
                    for symbol in file_symbols:
                        if symbol['type'] not in symbols_by_type:
                            symbols_by_type[symbol['type']] = []
                        symbols_by_type[symbol['type']].append(symbol)

                    # Add symbols by type
                    for symbol_type, symbols in symbols_by_type.items():
                        formatted_results += f"- {symbol_type.capitalize()}s: "
                        formatted_results += ", ".join([f"`{s['name']}`" for s in symbols[:5]])
                        if len(symbols) > 5:
                            formatted_results += f" and {len(symbols) - 5} more"
                        formatted_results += "\n"

                    formatted_results += "\n"
            except Exception as symbol_error:
                logger.warning(f"Error getting symbols for {file_path}: {str(symbol_error)}")

            # Add separator between results
            formatted_results += "---\n\n"

        # Add summary and guidance
        formatted_results += "## Summary\n\n"
        formatted_results += f"The search for '{information_request}' found matches in {len(results)} locations. "
        formatted_results += "The results are ranked by relevance score, with higher scores indicating better matches to your query.\n\n"
        formatted_results += "To get more specific information, try refining your query with more precise terms or asking about specific files or functions.\n"

        return formatted_results

    except Exception as e:
        error_msg = f"Error retrieving from codebase: {str(e)}"
        logger.error(error_msg)
        return error_msg
