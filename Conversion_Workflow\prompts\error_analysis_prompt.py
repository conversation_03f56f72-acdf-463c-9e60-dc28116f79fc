"""
Prompts for error analysis in Oracle to PostgreSQL conversion.
"""

def create_error_analysis_prompt(deployment_error_message: str, mappings_statements: list) -> str:
    """
    Creates a prompt for analyzing deployment errors in PostgreSQL and identifying
    the corresponding source (Oracle) and target (PostgreSQL) SQL statements using
    the existing mapping statements.

    Args:
        deployment_error_message: The error message from PostgreSQL deployment
        mappings_statements: List of dictionaries containing source and target statement mappings

    Returns:
        A formatted prompt string for the LLM
    """
    return f"""
        You are an expert SQL migration specialist focusing on Oracle to PostgreSQL conversions.

    TASK: Analyze the deployment error message from PostgreSQL and identify the EXACT source and target statement pair from the provided mappings that is causing the error.

    DEPLOYMENT ERROR MESSAGE:
    ```
    {deployment_error_message}
    ```

    AVAILABLE MAPPINGS (Source Oracle to Target PostgreSQL):
    ```json
    {mappings_statements}
    ```

        CRITICAL INSTRUCTIONS:
        1. Use ONLY the provided mappings to identify the problematic statement
        2. Identify the SPECIFIC mapping pair that contains the error based on the error message
        3. DO NOT create new statements or modify the existing ones
        4. Return EXACTLY ONE source statement and ONE target statement from the provided mappings

        For example, if the error message is "ERROR: syntax error at or near 'FRO'", find the mapping where the target statement contains "FRO" which should be "FROM".

        Return your response in this format:
        - source_statement: The exact Oracle SQL statement from the mappings that corresponds to the error
        - target_statement: The exact PostgreSQL SQL statement from the mappings that contains the error

        DO NOT include any explanations, comments, or multiple statements in your response.
        """