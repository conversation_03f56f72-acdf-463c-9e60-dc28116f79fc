
SET search_path TO BILLING;

CREATE OR REPLACE PROCEDURE billing.P_ACKNOWLEDGECASHTOCOUNTER (IC_UPDATEACK IN text, OC_<PERSON>LANCE INOUT refcursor)
LANGUAGE plpgsql
SECURITY DEFINER

AS $BODY$
DECLARE
    
    LV_UPDATEACK xml;

    LV_LOCATIONID billing.NATURALACCOUNTMASTER.LOCATIONID%type;

    LV_COMPANYID billing.NATURALACCOUNTMASTER.COMPANYID%type;

    LV_UPDATEDBY billing.TRANSACTION.UPDATEDBY%type;

    LV_TRANSACTIONID billing.TRANSACTION.TRANSACTIONID%type;

    LV_TRANAMOUNT billing.TRANSACTION.TRANAMOUNT%type;

    LV_TRANEVENT billing.TRANSACTION.TRANEVENT%type;

BEGIN
    SET search_path TO BILLING;

    LV_UPDATEACK := xml(IC_UPDATEACK);

    LV_TRANSACTIONID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@TransactionNo', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@TransactionNo', LV_UPDATEACK)))
        END)::text);

    LV_LOCATIONID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@LocationID', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@LocationID', LV_UPDATEACK)))
        END)::text);

    LV_COMPANYID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@CompanyID', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@CompanyID', LV_UPDATEACK)))
        END)::text);

    LV_UPDATEDBY := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text
        END);

    SAVEPOINT S_SAVEPOINT;
SELECT DISTINCT
    TRANEVENT INTO STRICT LV_TRANEVENT
FROM
    billing.TRANSACTION
WHERE
    TRANSACTIONID = LV_TRANSACTIONID
    AND ENTRYSTATUS = 'ENTERED'
    AND (REVERSALFLAG = 1
        AND DELETEFLAG = 1);

    CASE
    --START ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER
    WHEN LV_TRANEVENT = 'CASHTRANSFERCASHINHANDTOCOUNTER' THEN
        DECLARE LV_CASHACCOUNT varchar(20);
 LV_CASHINHAND varchar(20);

        BEGIN
            UPDATE
                billing.TRANSACTION
            SET
                ENTRYSTATUS = 'POSTED',
                UPDATEDBY = LV_UPDATEDBY,
                UPDATEDDATE = current_timestamp(0)::timestamp
            WHERE
                TRANSACTIONID = LV_TRANSACTIONID;

                SELECT
                    ACCOUNTNO,
                    TRANAMOUNT INTO STRICT LV_CASHACCOUNT,
                    LV_TRANAMOUNT
                FROM
                    billing.TRANSACTION
                WHERE
                    TRANSACTIONID = LV_TRANSACTIONID
                    AND TRANTYPE = 'DR';
 SELECT DISTINCT
                        ACCOUNTNO INTO STRICT LV_CASHINHAND
                    FROM
                        billing.TRANSACTION
                    WHERE
                        TRANSACTIONID = LV_TRANSACTIONID
                        AND TRANTYPE = 'CR';
 UPDATE
                            billing.NATURALACCOUNTMASTER
                        SET
                            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
                            UPDATEDBY = LV_UPDATEDBY,
                            UPDATEDDATE = current_timestamp(0)::timestamp
                        WHERE
                            ACCOUNTID = LV_CASHINHAND
                            AND ACCOUNTTYPE = 'CASHINHAND'
                            AND LOCATIONID = LV_LOCATIONID
                            AND COMPANYID = LV_COMPANYID;
 UPDATE
                                billing.CASHACCOUNTMASTER
                            SET
                                CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
                                UPDATEDBY = LV_UPDATEDBY,
                                UPDATEDDATE = current_timestamp(0)::timestamp
                            WHERE
                                ACCOUNTID = LV_CASHACCOUNT
                                AND LOCATIONID = LV_LOCATIONID
                                AND COMPANYID = LV_COMPANYID;
 OPEN OC_BALANCE FOR
                                    SELECT
                                        ACCOUNTID,
                                        ACCOUNTNAME,
                                        (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
                                    FROM
                                        billing.NATURALACCOUNTMASTER
                                    WHERE
                                        ACCOUNTID = LV_CASHINHAND
                                        AND COMPANYID = LV_LOCATIONID
                                        AND LOCATIONID = LV_LOCATIONID
                                    UNION
                                    SELECT
                                        ACCOUNTID,
                                        ACCOUNTNAME,
                                        (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
                                    FROM
                                        billing.CASHACCOUNTMASTER
                                    WHERE
                                        ACCOUNTID = LV_CASHACCOUNT
                                        AND COMPANYID = LV_LOCATIONID
                                        AND LOCATIONID = LV_LOCATIONID;

            END;

    --END ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER
    --START ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER
    WHEN LV_TRANEVENT = 'CASHTRANSFERCOUNTERTOCOUNTER' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);

    LV_TOCASHACCOUNT varchar(20);

    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;

        SELECT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_FROMCASHACCOUNT,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR';

        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_TOCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR';

        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TOCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID = LV_TOCASHACCOUNT
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID
            UNION
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID = LV_FROMCASHACCOUNT
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;

    END;

    --END ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER
    --START ACKNOWLEDGE CASH HANDOVER TO COUNTER
    WHEN LV_TRANEVENT = 'CASHHANDOVERTOCOUNTER' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);

    LV_TOCASHACCOUNT varchar(20);

    LV_FROMNONCASHACCOUNT varchar(20);

    LV_TONONCASHACCOUNT varchar(20);

    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;

        SELECT DISTINCT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_TOCASHACCOUNT,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE = 'CASH';

        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE = 'CASH';

        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TOCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_TONONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';

        SELECT
            SUM(TRANAMOUNT) INTO STRICT LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';

        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMNONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE != 'CASH';

        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TONONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMNONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_TOCASHACCOUNT, LV_TONONCASHACCOUNT, LV_FROMCASHACCOUNT, LV_FROMNONCASHACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;

    END;

    --END ACKNOWLEDGE CASH HANDOVER TO COUNTER
    --START ACKNOWLEDGE CASH HANDOVER TO CASHINHAND
    WHEN LV_TRANEVENT = 'CASHHANDOVERTOCASHINHAND' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);

    LV_CASHINHAND varchar(20);

    LV_FROMNONCASHACCOUNT varchar(20);

    LV_BANKACCOUNT varchar(20);

    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;

        SELECT DISTINCT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_CASHINHAND,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE = 'CASH';

        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE = 'CASH';

        UPDATE
            billing.NATURALACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_CASHINHAND
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_BANKACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';

        SELECT
            SUM(TRANAMOUNT) INTO STRICT LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';

        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMNONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE != 'CASH';

        UPDATE
            billing.NATURALACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_BANKACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMNONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;

        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.NATURALACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_CASHINHAND, LV_BANKACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID
            UNION
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_FROMCASHACCOUNT, LV_FROMNONCASHACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;

    END;

    --END ACKNOWLEDGE CASH HANDOVER TO CASHINHAND
    END CASE;

       -- END;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '% %', sqlstate, sqlerrm;

        --ROLLBACK TO S_SAVEPOINT;
END;

$BODY$;