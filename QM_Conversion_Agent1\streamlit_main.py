import streamlit as st
import uuid
import time
import sys
import io
from typing import Any
from contextlib import redirect_stdout

# Error handling for imports
try:
    from config import ConfigManager
    from llm import (
        AzureOpenAILLM,
        OpenAILLM,
        AnthropicLLM,
        GroqLLM,
        GeminiLLM,
        OllamaLLM
    )
    from workflow import GraphBuilder
    from dotenv import load_dotenv
    import os

    load_dotenv()
except ImportError as e:
    st.error(f"❌ Import Error: {str(e)}")
    st.error("Please ensure all dependencies are installed: pip install -r requirements_streamlit.txt")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="QMigrator AI - Oracle to PostgreSQL Migration",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79, #2e7d32);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }

    /* Text Area Styling - Black background, white text, white cursor */
    .stTextArea textarea {
        font-family: 'Courier New', monospace !important;
        font-size: 13px !important;
        line-height: 1.5 !important;
        padding: 12px !important;
        width: 100% !important;
        max-width: calc(100vw - 4rem) !important;
        box-sizing: border-box !important;
        background-color: #000000 !important;
        color: #ffffff !important;
        border: 1px solid #333333 !important;
        border-radius: 4px !important;
        caret-color: #ffffff !important;
        resize: vertical !important;
    }
    .stTextArea > div {
        width: 100% !important;
        max-width: calc(100vw - 4rem) !important;
        box-sizing: border-box !important;
        background-color: #000000 !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    .stTextArea {
        width: 100% !important;
        max-width: calc(100vw - 4rem) !important;
        box-sizing: border-box !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    .stTextArea textarea::placeholder {
        color: #cccccc !important;
        opacity: 0.7 !important;
    }
    .stTextArea textarea:focus {
        background-color: #000000 !important;
        color: #ffffff !important;
        border-color: #555555 !important;
        outline: none !important;
        caret-color: #ffffff !important;
        box-shadow: 0 0 0 2px rgba(85, 85, 85, 0.3) !important;
    }

    /* Fix text area container alignment */
    .stTextArea > div > div {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure proper text area positioning */
    .stTextArea label {
        color: #ffffff !important;
        font-weight: 500 !important;
        margin-bottom: 0.5rem !important;
    }

    /* Progress Bar Styling */
    .progress-container {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #e9ecef;
        margin: 1rem 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .progress-step {
        display: flex;
        align-items: center;
        padding: 0.8rem;
        margin: 0.5rem 0;
        border-radius: 8px;
        border-left: 4px solid #6c757d;
        background-color: #ffffff;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .progress-step.pending {
        border-left-color: #6c757d;
        background-color: #f8f9fa;
        color: #6c757d;
    }

    .progress-step.active {
        border-left-color: #28a745;
        background-color: #d4edda;
        color: #155724;
        font-weight: 600;
        animation: pulse 2s infinite;
        transform: scale(1.02);
    }

    .progress-step.completed {
        border-left-color: #007bff;
        background-color: #d1ecf1;
        color: #0c5460;
        font-weight: 500;
    }

    .progress-step.error {
        border-left-color: #dc3545;
        background-color: #f8d7da;
        color: #721c24;
        font-weight: 500;
    }

    @keyframes pulse {
        0% { opacity: 1; transform: scale(1.02); }
        50% { opacity: 0.8; transform: scale(1.01); }
        100% { opacity: 1; transform: scale(1.02); }
    }

    .step-icon {
        font-size: 1.2em;
        margin-right: 0.8rem;
        min-width: 1.5rem;
    }

    .step-text {
        flex: 1;
        font-size: 14px;
        line-height: 1.4;
    }

    .progress-header {
        text-align: center;
        margin-bottom: 1rem;
        padding: 0.5rem;
        background: linear-gradient(90deg, #e3f2fd, #f3e5f5);
        border-radius: 6px;
        border: 1px solid #bbdefb;
    }

    .current-node-highlight {
        background: linear-gradient(90deg, #d4edda, #c3e6cb);
        border: 2px solid #28a745;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        text-align: center;
        font-weight: bold;
        color: #155724;
        animation: glow 2s infinite;
    }

    @keyframes glow {
        0% { box-shadow: 0 0 5px #28a745; }
        50% { box-shadow: 0 0 20px #28a745; }
        100% { box-shadow: 0 0 5px #28a745; }
    }
</style>
""", unsafe_allow_html=True)

class WorkflowTracker:
    """Track workflow progress and status with real-time updates"""
    def __init__(self):
        self.steps = [
            "splitStatments",
            "AnalyzeError_identifyTargetStatements",
            "validate_error_identification",
            "mapSource_withTargetStatements",
            "validate_source_mapping",
            "Convert_TargetStatement",
            "validate_conversion",
            "replaceTargetStatement",
            "targetcode_deployment",
            "deployment_status"
        ]
        self.step_names = {
            "splitStatments": "📊 Split Statements",
            "AnalyzeError_identifyTargetStatements": "🔍 Identify Error Statements",
            "validate_error_identification": "✅ Validate Error Identification",
            "mapSource_withTargetStatements": "🔗 Map Source Statements",
            "validate_source_mapping": "✅ Validate Source Mapping",
            "Convert_TargetStatement": "🔄 Convert Statements",
            "validate_conversion": "✅ Validate Conversion",
            "replaceTargetStatement": "📝 Prepare for Deployment",
            "targetcode_deployment": "🚀 Deploy to PostgreSQL",
            "deployment_status": "📋 Check Deployment Status"
        }
        self.step_keywords = {
            "splitStatments": ["splitting", "split", "statements"],
            "AnalyzeError_identifyTargetStatements": ["analyzing", "error", "identifying", "target"],
            "validate_error_identification": ["validating", "error", "identification"],
            "mapSource_withTargetStatements": ["mapping", "source", "target"],
            "validate_source_mapping": ["validating", "source", "mapping"],
            "Convert_TargetStatement": ["converting", "target", "statement"],
            "validate_conversion": ["validating", "conversion"],
            "replaceTargetStatement": ["replacing", "target", "statement"],
            "targetcode_deployment": ["deploying", "deployment", "postgresql"],
            "deployment_status": ["checking", "deployment", "status"]
        }
        self.current_step = 0
        self.completed_steps = set()
        self.error_steps = set()
        self.iteration = 1

    def detect_current_step(self, text):
        """Detect which workflow step is currently running based on output text"""
        text_lower = text.lower()
        for step, keywords in self.step_keywords.items():
            if any(keyword.lower() in text_lower for keyword in keywords):
                return step
        return None

    def update_step(self, step_name, status="active"):
        """Update step status and current position"""
        if step_name in self.steps:
            step_index = self.steps.index(step_name)
            self.current_step = step_index

            if status == "completed":
                self.completed_steps.add(step_name)
            elif status == "error":
                self.error_steps.add(step_name)

    def get_progress_percentage(self):
        """Get overall progress percentage"""
        return (len(self.completed_steps) / len(self.steps)) * 100

def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """Create and initialize a Language Model instance"""
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")

@st.cache_resource
def setup_application() -> Any:
    """Set up the application with configuration and initialize the LLM"""
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        llm = create_llm(llm_provider, config_manager)
        return llm
    except Exception as e:
        st.error(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise

def display_real_time_progress(tracker: WorkflowTracker, current_node_placeholder=None):
    """Display real-time workflow progress with user-friendly visual indicators"""

    # Progress container
    st.markdown('<div class="progress-container">', unsafe_allow_html=True)

    # Progress header
    progress_percentage = tracker.get_progress_percentage()
    st.markdown(f"""
    <div class="progress-header">
        <h4>🚀 Workflow Progress: {progress_percentage:.1f}%</h4>
        <p>Iteration {tracker.iteration} | Step {tracker.current_step + 1} of {len(tracker.steps)}</p>
    </div>
    """, unsafe_allow_html=True)

    # Current node highlight
    if tracker.current_step < len(tracker.steps):
        current_step_name = tracker.step_names.get(tracker.steps[tracker.current_step], tracker.steps[tracker.current_step])
        if current_node_placeholder:
            with current_node_placeholder.container():
                st.markdown(f"""
                <div class="current-node-highlight">
                    🔄 <strong>Currently Executing:</strong> {current_step_name}
                </div>
                """, unsafe_allow_html=True)

    # Step-by-step progress with enhanced visuals
    for i, step in enumerate(tracker.steps):
        step_name = tracker.step_names.get(step, step)

        # Determine step status
        if step in tracker.error_steps:
            status_class = "error"
            status_icon = "❌"
        elif step in tracker.completed_steps:
            status_class = "completed"
            status_icon = "✅"
        elif i == tracker.current_step:
            status_class = "active"
            status_icon = "🔄"
        else:
            status_class = "pending"
            status_icon = "⏳"

        # Display step with enhanced styling
        st.markdown(f"""
        <div class="progress-step {status_class}">
            <div class="step-icon">{status_icon}</div>
            <div class="step-text">
                <strong>Step {i + 1}:</strong> {step_name}
            </div>
        </div>
        """, unsafe_allow_html=True)

    st.markdown('</div>', unsafe_allow_html=True)

class StreamlitWorkflowRunner:
    """Custom workflow runner that integrates with Streamlit UI"""

    def __init__(self, llm, tracker: WorkflowTracker):
        self.llm = llm
        self.tracker = tracker



    def run_workflow_with_ui(self, source_code: str, target_code: str, deployment_error: str):
        """Run workflow without UI display"""

        try:
            # Setup graph
            graph_builder = GraphBuilder(self.llm)
            graph_builder.setup_graph()

            # Save static workflow diagram
            graph_builder.save_graph_image(graph_builder.graph)

            # Create thread ID
            thread_id = f"thread_{uuid.uuid4()}"

            # Run the actual workflow
            result = graph_builder.invoke_graph({
                "source_code": source_code,
                "target_code": target_code,
                "deployment_error": deployment_error,
                "iteration_count": 1
            }, thread_id=thread_id)

            # Mark all steps as completed
            for step in self.tracker.steps:
                self.tracker.completed_steps.add(step)

            return result

        except Exception as e:
            st.error(f"❌ Workflow execution failed: {str(e)}")

            # Update progress to show error
            if self.tracker.current_step < len(self.tracker.steps):
                current_step = self.tracker.steps[self.tracker.current_step]
                self.tracker.error_steps.add(current_step)

            return None

def main():
    """Main Streamlit application"""

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🔄 QMigrator AI</h1>
        <h3>Oracle to PostgreSQL Database Migration Assistant</h3>
        <p>Intelligent AI-powered database code conversion with real-time progress tracking</p>
    </div>
    """, unsafe_allow_html=True)

    # Initialize session state
    if 'workflow_tracker' not in st.session_state:
        st.session_state.workflow_tracker = WorkflowTracker()
    if 'workflow_running' not in st.session_state:
        st.session_state.workflow_running = False
    if 'results' not in st.session_state:
        st.session_state.results = None
    if 'source_code' not in st.session_state:
        st.session_state.source_code = ""
    if 'target_code' not in st.session_state:
        st.session_state.target_code = ""
    if 'deployment_error' not in st.session_state:
        st.session_state.deployment_error = ""

    # Sidebar for workflow diagram only
    with st.sidebar:
        st.subheader("📊 Workflow Diagram")
        if os.path.exists("workflow_graph.png"):
            st.image("workflow_graph.png", caption="Migration Workflow", use_container_width=True)
        else:
            st.info("Workflow diagram will appear after first run")

    # Input tabs
    tab1, tab2, tab3 = st.tabs(["🔵 Oracle Source Code", "🟢 PostgreSQL Target Code", "❌ Deployment Error"])

    with tab1:
        source_code = st.text_area(
            "Oracle Source Code",
            value=st.session_state.source_code,
            height=300,
            help="Enter your Oracle PL/SQL source code here",
            placeholder="Paste your Oracle PL/SQL procedure or function code here...",
            key="source_code_input",
            on_change=lambda: setattr(st.session_state, 'source_code', st.session_state.source_code_input)
        )

    with tab2:
        target_code = st.text_area(
            "PostgreSQL Target Code",
            value=st.session_state.target_code,
            height=300,
            help="Enter your PostgreSQL target code with errors",
            placeholder="Paste your converted PostgreSQL code that has deployment errors...",
            key="target_code_input",
            on_change=lambda: setattr(st.session_state, 'target_code', st.session_state.target_code_input)
        )

    with tab3:
        deployment_error = st.text_area(
            "Deployment Error Message",
            value=st.session_state.deployment_error,
            height=200,
            help="Enter the PostgreSQL deployment error message",
            placeholder="Paste the error message from PostgreSQL deployment attempt...",
            key="deployment_error_input",
            on_change=lambda: setattr(st.session_state, 'deployment_error', st.session_state.deployment_error_input)
        )

    # Update session state with current values
    source_code = st.session_state.get('source_code_input', '')
    target_code = st.session_state.get('target_code_input', '')
    deployment_error = st.session_state.get('deployment_error_input', '')

    # Action buttons
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("🚀 Start Migration", type="primary", disabled=st.session_state.workflow_running):
            if source_code and target_code and deployment_error:
                st.session_state.workflow_running = True
                st.session_state.workflow_tracker = WorkflowTracker()

                # Initialize LLM
                try:
                    llm = setup_application()

                    # Show workflow progress
                    st.markdown("---")
                    st.subheader("Workflow progress:")

                    # Create progress placeholder
                    progress_placeholder = st.empty()

                    # Show migration started
                    with progress_placeholder.container():
                        st.info("Migration Started..")

                    # Run workflow
                    runner = StreamlitWorkflowRunner(llm, st.session_state.workflow_tracker)
                    st.session_state.results = runner.run_workflow_with_ui(
                        source_code, target_code, deployment_error
                    )

                    st.session_state.workflow_running = False

                    # Update progress with completion status
                    with progress_placeholder.container():
                        if st.session_state.results and st.session_state.results.get('deployment_successful'):
                            st.success("🎉 Migration completed successfully!")
                        elif st.session_state.results:
                            st.warning("⚠️ Migration completed with issues")
                        else:
                            st.error("❌ Migration failed")

                except Exception as e:
                    st.error(f"❌ Error: {str(e)}")
                    st.session_state.workflow_running = False
            else:
                st.error("❌ Please provide all required inputs")

    with col2:
        if st.button("🔄 Reset", disabled=st.session_state.workflow_running):
            # Clear all session state including widget keys
            st.session_state.workflow_tracker = WorkflowTracker()
            st.session_state.results = None
            st.session_state.source_code = ""
            st.session_state.target_code = ""
            st.session_state.deployment_error = ""
            # Clear widget keys
            if 'source_code_input' in st.session_state:
                del st.session_state.source_code_input
            if 'target_code_input' in st.session_state:
                del st.session_state.target_code_input
            if 'deployment_error_input' in st.session_state:
                del st.session_state.deployment_error_input
            st.rerun()

    with col3:
        if st.session_state.workflow_running:
            st.info("🔄 Workflow is running... Please wait")

    # Results section - Summary only
    if st.session_state.results:
        st.markdown("---")
        st.header("📋 Migration Results")

        # Display summary only
        if st.session_state.results.get('deployment_successful'):
            st.success("🎉 Migration completed successfully!")
        else:
            st.error("❌ Migration failed")

        # Display key metrics in a single row
        deployment_status = "Success" if st.session_state.results.get('deployment_successful') else "Failed"
        st.write(f"**Iterations:** {st.session_state.workflow_tracker.iteration} | **Steps Completed:** {len(st.session_state.workflow_tracker.completed_steps)} | **Deployment Status:** {deployment_status}")

        # Show output files info
        st.info("📁 Output files are saved in the 'output_files' directory")

if __name__ == "__main__":
    main()
